<template>
	<view class="login-container">
		<view class="login-header">
			<view class="logo-placeholder">🏠</view>
			<text class="app-name">租房小助手</text>
			<text class="welcome-text">欢迎回来</text>
		</view>

		<view class="login-form">
			<view class="form-item">
				<view class="input-wrapper">
					<text class="input-icon">👤</text>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入用户名" 
						v-model="formData.username"
						:maxlength="20"
					/>
				</view>
			</view>

			<view class="form-item">
				<view class="input-wrapper">
					<text class="input-icon">🔒</text>
					<input 
						class="form-input" 
						:type="showPassword ? 'text' : 'password'" 
						placeholder="请输入密码" 
						v-model="formData.password"
						:maxlength="20"
					/>
					<text class="password-toggle" @click="togglePassword">
						{{ showPassword ? '👁️' : '🙈' }}
					</text>
				</view>
			</view>

			<button 
				class="login-btn" 
				:class="{ 'btn-disabled': !canSubmit }"
				:disabled="!canSubmit || loading"
				@click="handleLogin"
			>
				{{ loading ? '登录中...' : '登录' }}
			</button>

			<view class="form-footer">
				<text class="register-link" @click="goToRegister">还没有账号？立即注册</text>
			</view>
		</view>

		<!-- 快速登录提示 -->
		<view class="quick-login-tip">
			<text class="tip-text">测试账号：testuser</text>
			<text class="tip-text">测试密码：123456</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			formData: {
				username: '',
				password: ''
			},
			showPassword: false,
			loading: false
		}
	},
	computed: {
		canSubmit() {
			return this.formData.username.trim() && this.formData.password.trim();
		}
	},
	methods: {
		// 切换密码显示状态
		togglePassword() {
			this.showPassword = !this.showPassword;
		},

		// 处理登录
		async handleLogin() {
			if (!this.canSubmit) {
				uni.showToast({
					title: '请填写完整信息',
					icon: 'none'
				});
				return;
			}

			this.loading = true;

			try {
				const res = await uniCloud.callFunction({
					name: 'user-auth',
					data: {
						action: 'login',
						data: {
							username: this.formData.username.trim(),
							password: this.formData.password.trim()
						}
					}
				});

				if (res.result.code === 200) {
					// 保存用户信息和token
					const userData = res.result.data;
					uni.setStorageSync('userToken', userData.token);
					uni.setStorageSync('userInfo', {
						userId: userData.userId,
						username: userData.username,
						nickname: userData.nickname,
						avatar: userData.avatar,
						phone: userData.phone
					});

					uni.showToast({
						title: '登录成功',
						icon: 'success'
					});

					// 延迟跳转，让用户看到成功提示
					setTimeout(() => {
						// 检查是否有来源页面，有则返回，没有则跳转到首页
						const pages = getCurrentPages();
						if (pages.length > 1) {
							uni.navigateBack();
						} else {
							uni.switchTab({
								url: '/pages/index/index'
							});
						}
					}, 1500);

				} else {
					uni.showToast({
						title: res.result.message || '登录失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('登录失败:', error);
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 跳转到注册页面
		goToRegister() {
			uni.navigateTo({
				url: '/pages/userRegister/userRegister'
			});
		}
	},

	onLoad() {
		// 检查是否已经登录
		const token = uni.getStorageSync('userToken');
		if (token) {
			uni.showModal({
				title: '提示',
				content: '您已经登录，是否重新登录？',
				success: (res) => {
					if (res.confirm) {
						// 清除登录信息
						uni.removeStorageSync('userToken');
						uni.removeStorageSync('userInfo');
					} else {
						// 返回上一页或首页
						const pages = getCurrentPages();
						if (pages.length > 1) {
							uni.navigateBack();
						} else {
							uni.switchTab({
								url: '/pages/index/index'
							});
						}
					}
				}
			});
		}
	}
}
</script>

<style scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 100rpx 60rpx 60rpx;
	display: flex;
	flex-direction: column;
}

.login-header {
	text-align: center;
	margin-bottom: 100rpx;
}

.logo-placeholder {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 30rpx;
	font-size: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.app-name {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #fff;
	margin-bottom: 20rpx;
}

.welcome-text {
	display: block;
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
}

.login-form {
	flex: 1;
}

.form-item {
	margin-bottom: 40rpx;
}

.input-wrapper {
	position: relative;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.input-icon {
	font-size: 32rpx;
	margin-right: 20rpx;
	color: #666;
}

.form-input {
	flex: 1;
	height: 100rpx;
	font-size: 32rpx;
	color: #333;
}

.password-toggle {
	font-size: 32rpx;
	color: #666;
	padding: 10rpx;
}

.login-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(45deg, #FF6B6B, #FF8E53);
	border: none;
	border-radius: 50rpx;
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	margin-top: 60rpx;
	box-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.3);
}

.login-btn:active {
	transform: translateY(2rpx);
}

.btn-disabled {
	background: #ccc !important;
	box-shadow: none !important;
}

.form-footer {
	text-align: center;
	margin-top: 60rpx;
}

.register-link {
	color: rgba(255, 255, 255, 0.9);
	font-size: 28rpx;
	text-decoration: underline;
}

.quick-login-tip {
	background-color: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-top: 60rpx;
	text-align: center;
}

.tip-text {
	display: block;
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
	margin-bottom: 10rpx;
}
</style>
