# 数据库初始化指南

## 问题说明

当您看到以下错误信息时：
```
检测到以下数据表未在云端创建，可能会影响云函数调试、运行
未创建的数据表列表如下：collections
```

这表示 UniCloud 云数据库中还没有创建相应的数据集合。

## 解决方案

### 方案一：使用云服务空间初始化向导（推荐）

1. **在 HBuilderX 中右键点击 `uniCloud-aliyun` 目录**
2. **选择"运行云服务空间初始化向导"**
3. **按照向导提示完成初始化**
4. **等待初始化完成**

### 方案二：使用数据库初始化云函数

1. **上传 db-init 云函数**
   - 右键 `uniCloud-aliyun/cloudfunctions/db-init` 目录
   - 选择"上传并运行"

2. **运行初始化云函数**
   - 在云函数列表中找到 `db-init`
   - 点击"运行"按钮
   - 查看运行结果

### 方案三：手动创建数据库集合

1. **打开 uniCloud 控制台**
   - 右键 `uniCloud-aliyun` 目录
   - 选择"打开uniCloud控制台"

2. **创建数据库集合**
   - 进入"云数据库"页面
   - 点击"新建集合"
   - 创建以下集合：
     - `user`（用户信息）
     - `house`（房源信息）

3. **导入数据库结构**
   - 在集合中点击"导入"
   - 选择对应的 schema 文件：
     - `user.schema.json`
     - `house.schema.json`

### 方案四：使用 JQL 查询创建数据

1. **打开 JQL 查询文件**
   - 在 HBuilderX 中打开 `uniCloud-aliyun/database/JQL查询.jql`

2. **执行创建语句**
   - 取消注释创建用户和房源的代码
   - 选中代码并按 F5 运行

## 初始化后的数据

### 测试用户
- **用户名**: testuser
- **密码**: 123456
- **昵称**: 测试用户
- **手机号**: 13800138000

### 测试房源
1. **精装一居室** - ¥3500/月（已审核）
2. **温馨二居室** - ¥5800/月（已审核）
3. **合租房间** - ¥2200/月（已审核）
4. **待审核房源** - ¥4200/月（待审核）

### 管理员账号
- **用户名**: admin
- **密码**: admin123

## 验证初始化结果

### 1. 检查数据库集合
在 uniCloud 控制台的云数据库中，应该能看到：
- `user` 集合（包含测试用户）
- `house` 集合（包含测试房源）

### 2. 测试云函数
运行以下测试：
```javascript
// 测试用户登录
uniCloud.callFunction({
  name: 'user-auth',
  data: {
    action: 'login',
    data: {
      username: 'testuser',
      password: '123456'
    }
  }
});

// 测试获取房源列表
uniCloud.callFunction({
  name: 'house-manage',
  data: {
    action: 'getHouseList',
    data: {
      page: 1,
      pageSize: 10
    }
  }
});
```

### 3. 测试前端页面
- 启动小程序
- 查看首页是否显示推荐房源
- 测试用户登录功能
- 测试房源列表和详情页面

## 常见问题

### Q: 初始化后仍然报错？
A: 请检查：
1. 云服务空间是否正确关联
2. 云函数是否全部上传成功
3. 数据库集合是否创建成功

### Q: 测试数据没有显示？
A: 请检查：
1. 数据是否成功插入数据库
2. 云函数权限是否正确配置
3. 前端 API 调用是否正确

### Q: 如何清空测试数据？
A: 在 uniCloud 控制台的云数据库中：
1. 进入对应集合
2. 选择要删除的记录
3. 点击删除按钮

## 注意事项

1. **生产环境**: 在生产环境中，请修改默认密码和测试数据
2. **数据安全**: 确保数据库权限配置正确
3. **备份**: 重要数据请及时备份
4. **监控**: 定期检查数据库使用情况和性能

## 联系支持

如果遇到问题，请：
1. 查看 HBuilderX 控制台的详细错误信息
2. 检查 uniCloud 控制台的日志
3. 参考 UniCloud 官方文档
4. 联系技术支持
