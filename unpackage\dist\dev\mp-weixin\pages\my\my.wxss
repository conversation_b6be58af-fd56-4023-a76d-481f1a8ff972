
.my-container.data-v-0be17cc6 {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 用户信息区域 */
.user-section.data-v-0be17cc6 {
	background-color: #fff;
	padding: 40rpx 30rpx;
	margin-bottom: 20rpx;
}
.user-info.data-v-0be17cc6,
.login-prompt.data-v-0be17cc6 {
	display: flex;
	align-items: center;
}
.user-avatar.data-v-0be17cc6 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	margin-right: 30rpx;
}
.user-details.data-v-0be17cc6,
.login-info.data-v-0be17cc6 {
	flex: 1;
}
.user-nickname.data-v-0be17cc6,
.login-text.data-v-0be17cc6 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}
.user-username.data-v-0be17cc6,
.login-tip.data-v-0be17cc6 {
	font-size: 26rpx;
	color: #999;
	display: block;
}
.edit-btn.data-v-0be17cc6,
.login-btn.data-v-0be17cc6 {
	background-color: #007AFF;
	color: #fff;
	border: none;
	border-radius: 30rpx;
	padding: 15rpx 30rpx;
	font-size: 26rpx;
}

/* 菜单区域 */
.menu-section.data-v-0be17cc6,
.other-section.data-v-0be17cc6 {
	background-color: #fff;
	margin-bottom: 20rpx;
}
.menu-item.data-v-0be17cc6 {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.menu-item.data-v-0be17cc6:last-child {
	border-bottom: none;
}
.menu-icon.data-v-0be17cc6 {
	font-size: 40rpx;
	margin-right: 30rpx;
}
.menu-text.data-v-0be17cc6 {
	flex: 1;
	font-size: 30rpx;
	color: #333;
}
.menu-count.data-v-0be17cc6 {
	background-color: #FF3B30;
	color: #fff;
	font-size: 20rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
	margin-right: 20rpx;
}
.menu-arrow.data-v-0be17cc6 {
	font-size: 28rpx;
	color: #999;
}

/* 退出登录 */
.logout-section.data-v-0be17cc6 {
	padding: 30rpx;
}
.logout-btn.data-v-0be17cc6 {
	width: 100%;
	height: 80rpx;
	background-color: #FF3B30;
	color: #fff;
	border: none;
	border-radius: 40rpx;
	font-size: 28rpx;
}

/* 弹窗样式 */
.modal.data-v-0be17cc6 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}
.modal-content.data-v-0be17cc6 {
	background-color: #fff;
	border-radius: 20rpx;
	width: 90%;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}
.modal-header.data-v-0be17cc6 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}
.modal-title.data-v-0be17cc6 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.modal-close.data-v-0be17cc6 {
	font-size: 40rpx;
	color: #999;
	padding: 10rpx;
}
.modal-body.data-v-0be17cc6 {
	flex: 1;
	padding: 20rpx;
}
.house-item.data-v-0be17cc6 {
	display: flex;
	background-color: #f8f9fa;
	border-radius: 15rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}
.house-image.data-v-0be17cc6 {
	width: 120rpx;
	height: 120rpx;
	border-radius: 10rpx;
	margin-right: 20rpx;
}
.house-info.data-v-0be17cc6 {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.house-title.data-v-0be17cc6 {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.house-price.data-v-0be17cc6 {
	font-size: 26rpx;
	color: #FF3B30;
	font-weight: bold;
	margin-bottom: 10rpx;
}
.house-location.data-v-0be17cc6 {
	font-size: 24rpx;
	color: #999;
}
.house-status.data-v-0be17cc6 {
	margin-top: 10rpx;
}
.status-text.data-v-0be17cc6 {
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 10rpx;
}
.status-pending.data-v-0be17cc6 {
	background-color: #FFF3E0;
	color: #FF9500;
}
.status-approved.data-v-0be17cc6 {
	background-color: #E8F5E8;
	color: #34C759;
}
.status-rejected.data-v-0be17cc6 {
	background-color: #FCE4EC;
	color: #FF3B30;
}
.empty-tip.data-v-0be17cc6 {
	text-align: center;
	color: #999;
	font-size: 28rpx;
	padding: 60rpx 20rpx;
}

