
.login-container.data-v-7264ae84 {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 100rpx 60rpx 60rpx;
	display: flex;
	flex-direction: column;
}
.login-header.data-v-7264ae84 {
	text-align: center;
	margin-bottom: 100rpx;
}
.logo-placeholder.data-v-7264ae84 {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 30rpx;
	font-size: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.app-name.data-v-7264ae84 {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #fff;
	margin-bottom: 20rpx;
}
.welcome-text.data-v-7264ae84 {
	display: block;
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
}
.login-form.data-v-7264ae84 {
	flex: 1;
}
.form-item.data-v-7264ae84 {
	margin-bottom: 40rpx;
}
.input-wrapper.data-v-7264ae84 {
	position: relative;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.input-icon.data-v-7264ae84 {
	font-size: 32rpx;
	margin-right: 20rpx;
	color: #666;
}
.form-input.data-v-7264ae84 {
	flex: 1;
	height: 100rpx;
	font-size: 32rpx;
	color: #333;
}
.password-toggle.data-v-7264ae84 {
	font-size: 32rpx;
	color: #666;
	padding: 10rpx;
}
.login-btn.data-v-7264ae84 {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(45deg, #FF6B6B, #FF8E53);
	border: none;
	border-radius: 50rpx;
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	margin-top: 60rpx;
	box-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.3);
}
.login-btn.data-v-7264ae84:active {
	-webkit-transform: translateY(2rpx);
	        transform: translateY(2rpx);
}
.btn-disabled.data-v-7264ae84 {
	background: #ccc !important;
	box-shadow: none !important;
}
.form-footer.data-v-7264ae84 {
	text-align: center;
	margin-top: 60rpx;
}
.register-link.data-v-7264ae84 {
	color: rgba(255, 255, 255, 0.9);
	font-size: 28rpx;
	text-decoration: underline;
}
.quick-login-tip.data-v-7264ae84 {
	background-color: rgba(255, 255, 255, 0.1);
	border-radius: 20rpx;
	padding: 30rpx;
	margin-top: 60rpx;
	text-align: center;
}
.tip-text.data-v-7264ae84 {
	display: block;
	color: rgba(255, 255, 255, 0.8);
	font-size: 24rpx;
	margin-bottom: 10rpx;
}

