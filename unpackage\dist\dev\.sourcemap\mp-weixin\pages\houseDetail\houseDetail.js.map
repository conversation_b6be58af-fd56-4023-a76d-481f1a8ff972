{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端11/pages/houseDetail/houseDetail.vue?100a", "webpack:///D:/web/project/前端11/pages/houseDetail/houseDetail.vue?1e09", "webpack:///D:/web/project/前端11/pages/houseDetail/houseDetail.vue?24ed", "webpack:///D:/web/project/前端11/pages/houseDetail/houseDetail.vue?2d0e", "uni-app:///pages/houseDetail/houseDetail.vue", "webpack:///D:/web/project/前端11/pages/houseDetail/houseDetail.vue?1e98", "webpack:///D:/web/project/前端11/pages/houseDetail/houseDetail.vue?3f59"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "houseId", "houseData", "images", "title", "desc", "price", "type", "config", "location", "address", "contact", "phone", "wechat", "created_at", "updated_at", "currentImageIndex", "isFavorited", "loading", "onLoad", "uni", "icon", "setTimeout", "methods", "loadHouseDetail", "houseManage", "res", "utils", "console", "checkFavoriteStatus", "userFavorites", "toggleFavorite", "content", "success", "previewImage", "urls", "current", "makePhoneCall", "phoneNumber", "fail", "copyWechat", "contactOwner", "actions", "itemList", "getConfigIcon", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,oBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACqC;;;AAG/F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrCA;AAAA;AAAA;AAAA;AAAsoB,CAAgB,ooBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC8F1pB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UAAAC;QAAA;QACAC;UAAAC;UAAAC;QAAA;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;MACA;MACA;MACA;IACA;MACAC;QACAhB;QACAiB;MACA;MACAC;QACAF;MACA;IACA;EACA;EAEAG;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBAEA;kBACA;kBACA;kBACA;oBACA;kBACA;gBACA;kBACAC;kBACAL;oBACAF;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAQ;gBACAD;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAF;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGAG;cAAA;gBAAAJ;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAE;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACAJ;kBAAA;kBAAA;gBAAA;gBACAP;kBACAhB;kBACA4B;kBACAC;oBACA;sBACAN;oBACA;kBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,KAMA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAG;cAAA;gBAAAJ;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEAI;cAAA;gBAAAJ;cAAA;gBAGA;kBACA;kBACAC;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAD;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MACA;MACAd;QACAe;QACAC;MACA;IACA;IAEA;IACAC;MACAjB;QACAkB;QACAC;UACAZ;QACA;MACA;IACA;IAEA;IACAa;MACApB;QACApB;QACAiC;UACAN;QACA;QACAY;UACAZ;QACA;MACA;IACA;IAEA;IACAc;MAAA;MACA;MAEA;QACAC;MACA;MACA;QACAA;MACA;MAEA;QACAf;QACA;MACA;MAEA;QACA;UACA;QACA;UACA;QACA;QACA;MACA;MAEAP;QACAuB;QACAV;UACA;YACA;UACA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAW;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpTA;AAAA;AAAA;AAAA;AAA67B,CAAgB,u5BAAG,EAAC,C;;;;;;;;;;;ACAj9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/houseDetail/houseDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/houseDetail/houseDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./houseDetail.vue?vue&type=template&id=6992ea2a&scoped=true&\"\nvar renderjs\nimport script from \"./houseDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./houseDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./houseDetail.vue?vue&type=style&index=0&id=6992ea2a&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6992ea2a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/houseDetail/houseDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseDetail.vue?vue&type=template&id=6992ea2a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.houseData.images.length\n  var g1 = _vm.houseData.config && _vm.houseData.config.length > 0\n  var l0 = g1\n    ? _vm.__map(_vm.houseData.config, function (config, __i1__) {\n        var $orig = _vm.__get_orig(config)\n        var m0 = _vm.getConfigIcon(config)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var m1 = _vm.formatTime(_vm.houseData.created_at)\n  var m2 =\n    _vm.houseData.updated_at !== _vm.houseData.created_at\n      ? _vm.formatTime(_vm.houseData.updated_at)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseDetail.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"house-detail-container\">\n\t\t<!-- 图片轮播 -->\n\t\t<view class=\"image-section\">\n\t\t\t<swiper class=\"image-swiper\" :indicator-dots=\"true\" :autoplay=\"false\" :duration=\"300\">\n\t\t\t\t<swiper-item v-for=\"(image, index) in houseData.images\" :key=\"index\">\n\t\t\t\t\t<image :src=\"image\" class=\"house-image\" mode=\"aspectFill\" @click=\"previewImage(index)\"></image>\n\t\t\t\t</swiper-item>\n\t\t\t</swiper>\n\t\t\t<view class=\"image-count\">{{ currentImageIndex + 1 }}/{{ houseData.images.length }}</view>\n\t\t</view>\n\n\t\t<!-- 基本信息 -->\n\t\t<view class=\"info-section\">\n\t\t\t<view class=\"price-section\">\n\t\t\t\t<text class=\"price\">¥{{ houseData.price }}</text>\n\t\t\t\t<text class=\"price-unit\">/月</text>\n\t\t\t\t<view class=\"favorite-btn\" :class=\"{ active: isFavorited }\" @click=\"toggleFavorite\">\n\t\t\t\t\t<text class=\"favorite-icon\">{{ isFavorited ? '❤️' : '🤍' }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<text class=\"house-title\">{{ houseData.title }}</text>\n\t\t\t\n\t\t\t<view class=\"house-tags\">\n\t\t\t\t<text class=\"house-tag primary\">{{ houseData.type }}</text>\n\t\t\t\t<text class=\"house-tag\" v-for=\"config in houseData.config\" :key=\"config\">{{ config }}</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"location-section\">\n\t\t\t\t<text class=\"location-icon\">📍</text>\n\t\t\t\t<text class=\"location-text\">{{ houseData.location.address }}</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 详细描述 -->\n\t\t<view class=\"desc-section\">\n\t\t\t<view class=\"section-title\">房源描述</view>\n\t\t\t<text class=\"desc-text\">{{ houseData.desc || '暂无详细描述' }}</text>\n\t\t</view>\n\n\t\t<!-- 房屋配置 -->\n\t\t<view class=\"config-section\" v-if=\"houseData.config && houseData.config.length > 0\">\n\t\t\t<view class=\"section-title\">房屋配置</view>\n\t\t\t<view class=\"config-grid\">\n\t\t\t\t<view class=\"config-item\" v-for=\"config in houseData.config\" :key=\"config\">\n\t\t\t\t\t<text class=\"config-icon\">{{ getConfigIcon(config) }}</text>\n\t\t\t\t\t<text class=\"config-text\">{{ config }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 联系房东 -->\n\t\t<view class=\"contact-section\">\n\t\t\t<view class=\"section-title\">联系方式</view>\n\t\t\t<view class=\"contact-item\" v-if=\"houseData.contact.phone\">\n\t\t\t\t<text class=\"contact-icon\">📞</text>\n\t\t\t\t<text class=\"contact-text\">{{ houseData.contact.phone }}</text>\n\t\t\t\t<button class=\"contact-btn\" @click=\"makePhoneCall\">拨打电话</button>\n\t\t\t</view>\n\t\t\t<view class=\"contact-item\" v-if=\"houseData.contact.wechat\">\n\t\t\t\t<text class=\"contact-icon\">💬</text>\n\t\t\t\t<text class=\"contact-text\">{{ houseData.contact.wechat }}</text>\n\t\t\t\t<button class=\"contact-btn\" @click=\"copyWechat\">复制微信</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 发布信息 -->\n\t\t<view class=\"publish-section\">\n\t\t\t<view class=\"section-title\">发布信息</view>\n\t\t\t<text class=\"publish-time\">发布时间：{{ formatTime(houseData.created_at) }}</text>\n\t\t\t<text class=\"update-time\" v-if=\"houseData.updated_at !== houseData.created_at\">\n\t\t\t\t更新时间：{{ formatTime(houseData.updated_at) }}\n\t\t\t</text>\n\t\t</view>\n\n\t\t<!-- 底部操作栏 -->\n\t\t<view class=\"bottom-actions\">\n\t\t\t<button class=\"action-btn secondary\" @click=\"toggleFavorite\">\n\t\t\t\t{{ isFavorited ? '取消收藏' : '收藏房源' }}\n\t\t\t</button>\n\t\t\t<button class=\"action-btn primary\" @click=\"contactOwner\">\n\t\t\t\t联系房东\n\t\t\t</button>\n\t\t</view>\n\n\t\t<!-- 加载状态 -->\n\t\t<view class=\"loading-section\" v-if=\"loading\">\n\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { houseManage, userFavorites, utils } from '@/utils/api.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\thouseId: '',\n\t\t\thouseData: {\n\t\t\t\timages: [],\n\t\t\t\ttitle: '',\n\t\t\t\tdesc: '',\n\t\t\t\tprice: 0,\n\t\t\t\ttype: '',\n\t\t\t\tconfig: [],\n\t\t\t\tlocation: { address: '' },\n\t\t\t\tcontact: { phone: '', wechat: '' },\n\t\t\t\tcreated_at: '',\n\t\t\t\tupdated_at: ''\n\t\t\t},\n\t\t\tcurrentImageIndex: 0,\n\t\t\tisFavorited: false,\n\t\t\tloading: true\n\t\t}\n\t},\n\t\n\tonLoad(options) {\n\t\tif (options.id) {\n\t\t\tthis.houseId = options.id;\n\t\t\tthis.loadHouseDetail();\n\t\t\tthis.checkFavoriteStatus();\n\t\t} else {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '房源ID不存在',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t\tsetTimeout(() => {\n\t\t\t\tuni.navigateBack();\n\t\t\t}, 1500);\n\t\t}\n\t},\n\t\n\tmethods: {\n\t\t// 加载房源详情\n\t\tasync loadHouseDetail() {\n\t\t\tthis.loading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await houseManage.getHouseDetail(this.houseId);\n\t\t\t\t\n\t\t\t\tif (res.code === 200) {\n\t\t\t\t\tthis.houseData = res.data;\n\t\t\t\t\t// 如果没有图片，添加默认图片\n\t\t\t\t\tif (!this.houseData.images || this.houseData.images.length === 0) {\n\t\t\t\t\t\tthis.houseData.images = ['https://via.placeholder.com/750x500/CCCCCC/FFFFFF?text=暂无图片'];\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tutils.showError(res.message || '加载失败');\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t}, 1500);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载房源详情失败:', error);\n\t\t\t\tutils.showError('网络错误');\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 检查收藏状态\n\t\tasync checkFavoriteStatus() {\n\t\t\tif (!utils.isLoggedIn()) return;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await userFavorites.checkFavorite(this.houseId);\n\t\t\t\tif (res.code === 200) {\n\t\t\t\t\tthis.isFavorited = res.data.isFavorited;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('检查收藏状态失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 切换收藏状态\n\t\tasync toggleFavorite() {\n\t\t\tif (!utils.isLoggedIn()) {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '提示',\n\t\t\t\t\tcontent: '请先登录后再收藏房源',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\tutils.goToLogin();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tlet res;\n\t\t\t\tif (this.isFavorited) {\n\t\t\t\t\tres = await userFavorites.removeFavorite(this.houseId);\n\t\t\t\t} else {\n\t\t\t\t\tres = await userFavorites.addFavorite(this.houseId);\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (res.code === 200) {\n\t\t\t\t\tthis.isFavorited = !this.isFavorited;\n\t\t\t\t\tutils.showSuccess(res.message);\n\t\t\t\t} else {\n\t\t\t\t\tutils.showError(res.message);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('收藏操作失败:', error);\n\t\t\t\tutils.showError('操作失败');\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 预览图片\n\t\tpreviewImage(index) {\n\t\t\tthis.currentImageIndex = index;\n\t\t\tuni.previewImage({\n\t\t\t\turls: this.houseData.images,\n\t\t\t\tcurrent: index\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 拨打电话\n\t\tmakePhoneCall() {\n\t\t\tuni.makePhoneCall({\n\t\t\t\tphoneNumber: this.houseData.contact.phone,\n\t\t\t\tfail: () => {\n\t\t\t\t\tutils.showError('拨打电话失败');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 复制微信号\n\t\tcopyWechat() {\n\t\t\tuni.setClipboardData({\n\t\t\t\tdata: this.houseData.contact.wechat,\n\t\t\t\tsuccess: () => {\n\t\t\t\t\tutils.showSuccess('微信号已复制');\n\t\t\t\t},\n\t\t\t\tfail: () => {\n\t\t\t\t\tutils.showError('复制失败');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 联系房东\n\t\tcontactOwner() {\n\t\t\tconst actions = [];\n\t\t\t\n\t\t\tif (this.houseData.contact.phone) {\n\t\t\t\tactions.push('拨打电话');\n\t\t\t}\n\t\t\tif (this.houseData.contact.wechat) {\n\t\t\t\tactions.push('复制微信');\n\t\t\t}\n\t\t\t\n\t\t\tif (actions.length === 0) {\n\t\t\t\tutils.showError('暂无联系方式');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tif (actions.length === 1) {\n\t\t\t\tif (actions[0] === '拨打电话') {\n\t\t\t\t\tthis.makePhoneCall();\n\t\t\t\t} else {\n\t\t\t\t\tthis.copyWechat();\n\t\t\t\t}\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tuni.showActionSheet({\n\t\t\t\titemList: actions,\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (actions[res.tapIndex] === '拨打电话') {\n\t\t\t\t\t\tthis.makePhoneCall();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.copyWechat();\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 获取配置图标\n\t\tgetConfigIcon(config) {\n\t\t\tconst iconMap = {\n\t\t\t\t'空调': '❄️',\n\t\t\t\t'洗衣机': '👕',\n\t\t\t\t'冰箱': '🧊',\n\t\t\t\t'热水器': '🚿',\n\t\t\t\t'宽带': '📶',\n\t\t\t\t'电视': '📺',\n\t\t\t\t'沙发': '🛋️',\n\t\t\t\t'床': '🛏️',\n\t\t\t\t'衣柜': '👗',\n\t\t\t\t'书桌': '📚',\n\t\t\t\t'微波炉': '🔥',\n\t\t\t\t'燃气灶': '🔥',\n\t\t\t\t'油烟机': '💨',\n\t\t\t\t'独立卫生间': '🚽',\n\t\t\t\t'阳台': '🌿',\n\t\t\t\t'停车位': '🚗'\n\t\t\t};\n\t\t\treturn iconMap[config] || '✅';\n\t\t},\n\t\t\n\t\t// 格式化时间\n\t\tformatTime(timestamp) {\n\t\t\treturn utils.formatTime(timestamp);\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.house-detail-container {\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n\tpadding-bottom: 120rpx;\n}\n\n/* 图片轮播样式 */\n.image-section {\n\tposition: relative;\n\theight: 500rpx;\n}\n\n.image-swiper {\n\theight: 100%;\n}\n\n.house-image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.image-count {\n\tposition: absolute;\n\tbottom: 20rpx;\n\tright: 20rpx;\n\tbackground-color: rgba(0, 0, 0, 0.6);\n\tcolor: #fff;\n\tpadding: 10rpx 20rpx;\n\tborder-radius: 20rpx;\n\tfont-size: 24rpx;\n}\n\n/* 基本信息样式 */\n.info-section {\n\tbackground-color: #fff;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.price-section {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.price {\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\tcolor: #FF3B30;\n}\n\n.price-unit {\n\tfont-size: 28rpx;\n\tcolor: #999;\n\tmargin-left: 10rpx;\n}\n\n.favorite-btn {\n\tmargin-left: auto;\n\tpadding: 10rpx;\n}\n\n.favorite-icon {\n\tfont-size: 48rpx;\n}\n\n.house-title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n\tdisplay: block;\n}\n\n.house-tags {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tmargin-bottom: 20rpx;\n}\n\n.house-tag {\n\tbackground-color: #f0f0f0;\n\tcolor: #666;\n\tfont-size: 24rpx;\n\tpadding: 10rpx 20rpx;\n\tborder-radius: 20rpx;\n\tmargin-right: 15rpx;\n\tmargin-bottom: 10rpx;\n}\n\n.house-tag.primary {\n\tbackground-color: #E3F2FD;\n\tcolor: #007AFF;\n}\n\n.location-section {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.location-icon {\n\tfont-size: 28rpx;\n\tmargin-right: 10rpx;\n}\n\n.location-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n/* 描述样式 */\n.desc-section {\n\tbackground-color: #fff;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n\tdisplay: block;\n}\n\n.desc-text {\n\tfont-size: 28rpx;\n\tcolor: #666;\n\tline-height: 1.6;\n\tdisplay: block;\n}\n\n/* 配置样式 */\n.config-section {\n\tbackground-color: #fff;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.config-grid {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(3, 1fr);\n\tgap: 30rpx;\n}\n\n.config-item {\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\ttext-align: center;\n}\n\n.config-icon {\n\tfont-size: 40rpx;\n\tmargin-bottom: 10rpx;\n}\n\n.config-text {\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n/* 联系方式样式 */\n.contact-section {\n\tbackground-color: #fff;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.contact-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.contact-item:last-child {\n\tborder-bottom: none;\n}\n\n.contact-icon {\n\tfont-size: 32rpx;\n\tmargin-right: 20rpx;\n}\n\n.contact-text {\n\tflex: 1;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.contact-btn {\n\tbackground-color: #007AFF;\n\tcolor: #fff;\n\tborder: none;\n\tborder-radius: 30rpx;\n\tpadding: 15rpx 30rpx;\n\tfont-size: 24rpx;\n}\n\n/* 发布信息样式 */\n.publish-section {\n\tbackground-color: #fff;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.publish-time,\n.update-time {\n\tfont-size: 26rpx;\n\tcolor: #999;\n\tdisplay: block;\n\tmargin-bottom: 10rpx;\n}\n\n/* 底部操作栏样式 */\n.bottom-actions {\n\tposition: fixed;\n\tbottom: 0;\n\tleft: 0;\n\tright: 0;\n\tbackground-color: #fff;\n\tpadding: 20rpx 30rpx;\n\tborder-top: 1rpx solid #eee;\n\tdisplay: flex;\n\tgap: 20rpx;\n\tz-index: 100;\n}\n\n.action-btn {\n\tflex: 1;\n\theight: 80rpx;\n\tborder: none;\n\tborder-radius: 40rpx;\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n}\n\n.action-btn.primary {\n\tbackground-color: #007AFF;\n\tcolor: #fff;\n}\n\n.action-btn.secondary {\n\tbackground-color: #f8f9fa;\n\tcolor: #666;\n}\n\n/* 加载状态样式 */\n.loading-section {\n\tpadding: 100rpx;\n\ttext-align: center;\n}\n\n.loading-text {\n\tcolor: #999;\n\tfont-size: 28rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseDetail.vue?vue&type=style&index=0&id=6992ea2a&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseDetail.vue?vue&type=style&index=0&id=6992ea2a&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754093677267\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}