{"bsonType": "object", "description": "房源信息集合", "required": [], "properties": {"_id": {"description": "房源ID，系统自动生成"}, "title": {"bsonType": "string", "description": "房源标题", "minLength": 5, "maxLength": 100, "title": "房源标题"}, "desc": {"bsonType": "string", "description": "房源详细描述", "maxLength": 1000, "title": "房源描述"}, "images": {"bsonType": "array", "description": "房源图片URL数组", "items": {"bsonType": "string"}, "maxItems": 10, "title": "房源图片"}, "location": {"bsonType": "object", "description": "位置信息", "required": ["address"], "properties": {"latitude": {"bsonType": "double", "description": "纬度", "minimum": -90, "maximum": 90, "title": "纬度"}, "longitude": {"bsonType": "double", "description": "经度", "minimum": -180, "maximum": 180, "title": "经度"}, "address": {"bsonType": "string", "description": "详细地址", "maxLength": 200, "title": "地址"}}, "title": "位置信息"}, "price": {"bsonType": "int", "description": "月租金（元）", "minimum": 100, "maximum": 50000, "title": "租金"}, "type": {"bsonType": "string", "description": "房型", "enum": ["一居室", "二居室", "三居室", "四居室", "合租", "单间", "其他"], "title": "房型"}, "config": {"bsonType": "array", "description": "房屋配置", "items": {"bsonType": "string", "enum": ["空调", "洗衣机", "冰箱", "热水器", "宽带", "电视", "沙发", "床", "衣柜", "书桌", "微波炉", "燃气灶", "油烟机", "独立卫生间", "阳台", "停车位"]}, "title": "房屋配置"}, "contact": {"bsonType": "object", "description": "联系方式", "properties": {"phone": {"bsonType": "string", "description": "联系电话", "pattern": "^1[3-9]\\d{9}$", "title": "联系电话"}, "wechat": {"bsonType": "string", "description": "微信号", "maxLength": 50, "title": "微信号"}}, "title": "联系方式"}, "owner_id": {"bsonType": "string", "description": "发布用户ID", "title": "发布者"}, "status": {"bsonType": "string", "description": "审核状态", "enum": ["pending", "approved", "rejected"], "default": "pending", "title": "审核状态"}, "created_at": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}, "title": "创建时间"}, "updated_at": {"bsonType": "timestamp", "description": "更新时间", "title": "更新时间"}}, "permission": {"read": "doc.status == 'approved' || auth.uid == doc.owner_id || 'admin' in auth.role", "create": "auth.uid != null", "update": "auth.uid == doc.owner_id || 'admin' in auth.role", "delete": "auth.uid == doc.owner_id || 'admin' in auth.role"}}