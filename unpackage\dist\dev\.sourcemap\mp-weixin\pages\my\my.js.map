{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端11/pages/my/my.vue?87af", "webpack:///D:/web/project/前端11/pages/my/my.vue?5e51", "webpack:///D:/web/project/前端11/pages/my/my.vue?87e7", "webpack:///D:/web/project/前端11/pages/my/my.vue?ec7a", "uni-app:///pages/my/my.vue", "webpack:///D:/web/project/前端11/pages/my/my.vue?a5f3", "webpack:///D:/web/project/前端11/pages/my/my.vue?e4d4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "isLoggedIn", "myHousesCount", "favoritesCount", "showMyHousesModal", "showFavoritesModal", "myHouses", "favorites", "onShow", "onLoad", "methods", "checkLoginStatus", "loadUserData", "Promise", "loadMyHousesCount", "houseManage", "page", "pageSize", "res", "console", "loadFavoritesCount", "userFavorites", "goToLogin", "utils", "goToEditProfile", "uni", "title", "icon", "goToMyHouses", "goToMyFavorites", "goToSettings", "contactService", "content", "showCancel", "showAbout", "handleLogout", "success", "closeMyHousesModal", "closeFavoritesModal", "goToHouseDetail", "url", "getStatusText"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,WAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACsD;AACL;AACqC;;;AAGtF;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,wEAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAA6nB,CAAgB,2nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC0HjpB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC,aACA,2BACA,2BACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;kBAAAC;kBAAAC;gBAAA;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEAC;kBAAAL;kBAAAC;gBAAA;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MACAC;IACA;IAEA;IACAC;MACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAb;kBAAAC;kBAAAC;gBAAA;cAAA;gBAAAC;gBACA;kBACA;kBACA;gBACA;kBACAK;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;gBACAI;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA,OAKAR;kBAAAL;kBAAAC;gBAAA;cAAA;gBAAAC;gBACA;kBACA;kBACA;gBACA;kBACAK;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAJ;gBACAI;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MACAL;QACAC;QACAC;MACA;IACA;IAEA;IACAI;MACAN;QACAC;QACAM;QACAC;MACA;IACA;IAEA;IACAC;MACAT;QACAC;QACAM;QACAC;MACA;IACA;IAEA;IACAE;MAAA;MACAV;QACAC;QACAM;QACAI;UACA;YACAb;YACA;YACAA;UACA;QACA;MACA;IACA;IAEA;IACAc;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACAd;QACAe;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5TA;AAAA;AAAA;AAAA;AAAo7B,CAAgB,84BAAG,EAAC,C;;;;;;;;;;;ACAx8B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/my.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/my/my.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my.vue?vue&type=template&id=0be17cc6&scoped=true&\"\nvar renderjs\nimport script from \"./my.vue?vue&type=script&lang=js&\"\nexport * from \"./my.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my.vue?vue&type=style&index=0&id=0be17cc6&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0be17cc6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/my.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=template&id=0be17cc6&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.showMyHousesModal\n    ? _vm.__map(_vm.myHouses, function (house, __i0__) {\n        var $orig = _vm.__get_orig(house)\n        var m0 = _vm.getStatusText(house.status)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var g0 = _vm.showMyHousesModal ? _vm.myHouses.length : null\n  var g1 = _vm.showFavoritesModal ? _vm.favorites.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"my-container\">\n\t\t<!-- 用户信息区域 -->\n\t\t<view class=\"user-section\">\n\t\t\t<view class=\"user-info\" v-if=\"isLoggedIn\">\n\t\t\t\t<view class=\"user-avatar\" v-if=\"!userInfo.avatar\">👤</view>\n\t\t\t\t<image v-else :src=\"userInfo.avatar\" class=\"user-avatar\" mode=\"aspectFill\"></image>\n\t\t\t\t<view class=\"user-details\">\n\t\t\t\t\t<text class=\"user-nickname\">{{ userInfo.nickname || userInfo.username }}</text>\n\t\t\t\t\t<text class=\"user-username\">@{{ userInfo.username }}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"user-actions\">\n\t\t\t\t\t<button class=\"edit-btn\" @click=\"goToEditProfile\">编辑</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"login-prompt\" v-else>\n\t\t\t\t<view class=\"user-avatar\">👤</view>\n\t\t\t\t<view class=\"login-info\">\n\t\t\t\t\t<text class=\"login-text\">点击登录</text>\n\t\t\t\t\t<text class=\"login-tip\">登录后可发布和收藏房源</text>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"login-btn\" @click=\"goToLogin\">登录</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 功能菜单 -->\n\t\t<view class=\"menu-section\">\n\t\t\t<view class=\"menu-item\" @click=\"goToMyHouses\">\n\t\t\t\t<text class=\"menu-icon\">🏠</text>\n\t\t\t\t<text class=\"menu-text\">我的发布</text>\n\t\t\t\t<text class=\"menu-count\" v-if=\"myHousesCount > 0\">{{ myHousesCount }}</text>\n\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"menu-item\" @click=\"goToMyFavorites\">\n\t\t\t\t<text class=\"menu-icon\">❤️</text>\n\t\t\t\t<text class=\"menu-text\">我的收藏</text>\n\t\t\t\t<text class=\"menu-count\" v-if=\"favoritesCount > 0\">{{ favoritesCount }}</text>\n\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"menu-item\" @click=\"goToSettings\">\n\t\t\t\t<text class=\"menu-icon\">⚙️</text>\n\t\t\t\t<text class=\"menu-text\">设置</text>\n\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 其他功能 -->\n\t\t<view class=\"other-section\">\n\t\t\t<view class=\"menu-item\" @click=\"contactService\">\n\t\t\t\t<text class=\"menu-icon\">💬</text>\n\t\t\t\t<text class=\"menu-text\">联系客服</text>\n\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"menu-item\" @click=\"showAbout\">\n\t\t\t\t<text class=\"menu-icon\">ℹ️</text>\n\t\t\t\t<text class=\"menu-text\">关于我们</text>\n\t\t\t\t<text class=\"menu-arrow\">></text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 退出登录 -->\n\t\t<view class=\"logout-section\" v-if=\"isLoggedIn\">\n\t\t\t<button class=\"logout-btn\" @click=\"handleLogout\">退出登录</button>\n\t\t</view>\n\n\t\t<!-- 我的发布弹窗 -->\n\t\t<view class=\"modal\" v-if=\"showMyHousesModal\" @click=\"closeMyHousesModal\">\n\t\t\t<view class=\"modal-content\" @click.stop>\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">我的发布</text>\n\t\t\t\t\t<text class=\"modal-close\" @click=\"closeMyHousesModal\">✕</text>\n\t\t\t\t</view>\n\t\t\t\t<scroll-view class=\"modal-body\" scroll-y=\"true\">\n\t\t\t\t\t<view class=\"house-item\" v-for=\"house in myHouses\" :key=\"house._id\" @click=\"goToHouseDetail(house._id)\">\n\t\t\t\t\t\t<image :src=\"house.images[0] || 'https://via.placeholder.com/240x180/CCCCCC/FFFFFF?text=暂无图片'\" class=\"house-image\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t<view class=\"house-info\">\n\t\t\t\t\t\t\t<text class=\"house-title\">{{ house.title }}</text>\n\t\t\t\t\t\t\t<text class=\"house-price\">¥{{ house.price }}/月</text>\n\t\t\t\t\t\t\t<view class=\"house-status\">\n\t\t\t\t\t\t\t\t<text class=\"status-text\" :class=\"'status-' + house.status\">\n\t\t\t\t\t\t\t\t\t{{ getStatusText(house.status) }}\n\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"empty-tip\" v-if=\"myHouses.length === 0\">\n\t\t\t\t\t\t暂无发布的房源\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 我的收藏弹窗 -->\n\t\t<view class=\"modal\" v-if=\"showFavoritesModal\" @click=\"closeFavoritesModal\">\n\t\t\t<view class=\"modal-content\" @click.stop>\n\t\t\t\t<view class=\"modal-header\">\n\t\t\t\t\t<text class=\"modal-title\">我的收藏</text>\n\t\t\t\t\t<text class=\"modal-close\" @click=\"closeFavoritesModal\">✕</text>\n\t\t\t\t</view>\n\t\t\t\t<scroll-view class=\"modal-body\" scroll-y=\"true\">\n\t\t\t\t\t<view class=\"house-item\" v-for=\"house in favorites\" :key=\"house._id\" @click=\"goToHouseDetail(house._id)\">\n\t\t\t\t\t\t<image :src=\"house.images[0] || 'https://via.placeholder.com/240x180/CCCCCC/FFFFFF?text=暂无图片'\" class=\"house-image\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t<view class=\"house-info\">\n\t\t\t\t\t\t\t<text class=\"house-title\">{{ house.title }}</text>\n\t\t\t\t\t\t\t<text class=\"house-price\">¥{{ house.price }}/月</text>\n\t\t\t\t\t\t\t<text class=\"house-location\">{{ house.location.address }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"empty-tip\" v-if=\"favorites.length === 0\">\n\t\t\t\t\t\t暂无收藏的房源\n\t\t\t\t\t</view>\n\t\t\t\t</scroll-view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nimport { houseManage, userFavorites, utils } from '@/utils/api.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tuserInfo: {},\n\t\t\tisLoggedIn: false,\n\t\t\tmyHousesCount: 0,\n\t\t\tfavoritesCount: 0,\n\t\t\tshowMyHousesModal: false,\n\t\t\tshowFavoritesModal: false,\n\t\t\tmyHouses: [],\n\t\t\tfavorites: []\n\t\t}\n\t},\n\t\n\tonShow() {\n\t\tthis.checkLoginStatus();\n\t\tif (this.isLoggedIn) {\n\t\t\tthis.loadUserData();\n\t\t}\n\t},\n\t\n\tonLoad(options) {\n\t\t// 检查是否有特定的tab参数\n\t\tif (options.tab === 'favorites') {\n\t\t\tthis.goToMyFavorites();\n\t\t}\n\t},\n\t\n\tmethods: {\n\t\t// 检查登录状态\n\t\tcheckLoginStatus() {\n\t\t\tthis.isLoggedIn = utils.isLoggedIn();\n\t\t\tif (this.isLoggedIn) {\n\t\t\t\tthis.userInfo = utils.getUserInfo();\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载用户数据\n\t\tasync loadUserData() {\n\t\t\tawait Promise.all([\n\t\t\t\tthis.loadMyHousesCount(),\n\t\t\t\tthis.loadFavoritesCount()\n\t\t\t]);\n\t\t},\n\t\t\n\t\t// 加载我的发布数量\n\t\tasync loadMyHousesCount() {\n\t\t\ttry {\n\t\t\t\tconst res = await houseManage.getMyHouses({ page: 1, pageSize: 1 });\n\t\t\t\tif (res.code === 200) {\n\t\t\t\t\tthis.myHousesCount = res.data.total;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载我的发布数量失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 加载收藏数量\n\t\tasync loadFavoritesCount() {\n\t\t\ttry {\n\t\t\t\tconst res = await userFavorites.getFavorites({ page: 1, pageSize: 1 });\n\t\t\t\tif (res.code === 200) {\n\t\t\t\t\tthis.favoritesCount = res.data.total;\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载收藏数量失败:', error);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 跳转到登录页面\n\t\tgoToLogin() {\n\t\t\tutils.goToLogin();\n\t\t},\n\t\t\n\t\t// 跳转到编辑资料页面\n\t\tgoToEditProfile() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '编辑资料功能开发中',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 显示我的发布\n\t\tasync goToMyHouses() {\n\t\t\tif (!this.isLoggedIn) {\n\t\t\t\tthis.goToLogin();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await houseManage.getMyHouses({ page: 1, pageSize: 20 });\n\t\t\t\tif (res.code === 200) {\n\t\t\t\t\tthis.myHouses = res.data.list;\n\t\t\t\t\tthis.showMyHousesModal = true;\n\t\t\t\t} else {\n\t\t\t\t\tutils.showError(res.message);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载我的发布失败:', error);\n\t\t\t\tutils.showError('加载失败');\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 显示我的收藏\n\t\tasync goToMyFavorites() {\n\t\t\tif (!this.isLoggedIn) {\n\t\t\t\tthis.goToLogin();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst res = await userFavorites.getFavorites({ page: 1, pageSize: 20 });\n\t\t\t\tif (res.code === 200) {\n\t\t\t\t\tthis.favorites = res.data.list;\n\t\t\t\t\tthis.showFavoritesModal = true;\n\t\t\t\t} else {\n\t\t\t\t\tutils.showError(res.message);\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载我的收藏失败:', error);\n\t\t\t\tutils.showError('加载失败');\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 跳转到设置页面\n\t\tgoToSettings() {\n\t\t\tuni.showToast({\n\t\t\t\ttitle: '设置功能开发中',\n\t\t\t\ticon: 'none'\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 联系客服\n\t\tcontactService() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '联系客服',\n\t\t\t\tcontent: '客服电话：400-123-4567\\n工作时间：9:00-18:00\\n\\n或添加客服微信：service123',\n\t\t\t\tshowCancel: false\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 显示关于我们\n\t\tshowAbout() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '关于我们',\n\t\t\t\tcontent: '租房小助手 v1.0.0\\n\\n一个专业的租房信息平台\\n为您提供优质的租房服务',\n\t\t\t\tshowCancel: false\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 退出登录\n\t\thandleLogout() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '确认退出',\n\t\t\t\tcontent: '确定要退出登录吗？',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tutils.clearUserData();\n\t\t\t\t\t\tthis.checkLoginStatus();\n\t\t\t\t\t\tutils.showSuccess('已退出登录');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 关闭我的发布弹窗\n\t\tcloseMyHousesModal() {\n\t\t\tthis.showMyHousesModal = false;\n\t\t},\n\t\t\n\t\t// 关闭我的收藏弹窗\n\t\tcloseFavoritesModal() {\n\t\t\tthis.showFavoritesModal = false;\n\t\t},\n\t\t\n\t\t// 跳转到房源详情\n\t\tgoToHouseDetail(houseId) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/houseDetail/houseDetail?id=${houseId}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 获取状态文本\n\t\tgetStatusText(status) {\n\t\t\tconst statusMap = {\n\t\t\t\t'pending': '待审核',\n\t\t\t\t'approved': '已通过',\n\t\t\t\t'rejected': '已驳回'\n\t\t\t};\n\t\t\treturn statusMap[status] || '未知';\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.my-container {\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n}\n\n/* 用户信息区域 */\n.user-section {\n\tbackground-color: #fff;\n\tpadding: 40rpx 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.user-info,\n.login-prompt {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.user-avatar {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 60rpx;\n\tmargin-right: 30rpx;\n}\n\n.user-details,\n.login-info {\n\tflex: 1;\n}\n\n.user-nickname,\n.login-text {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tdisplay: block;\n\tmargin-bottom: 10rpx;\n}\n\n.user-username,\n.login-tip {\n\tfont-size: 26rpx;\n\tcolor: #999;\n\tdisplay: block;\n}\n\n.edit-btn,\n.login-btn {\n\tbackground-color: #007AFF;\n\tcolor: #fff;\n\tborder: none;\n\tborder-radius: 30rpx;\n\tpadding: 15rpx 30rpx;\n\tfont-size: 26rpx;\n}\n\n/* 菜单区域 */\n.menu-section,\n.other-section {\n\tbackground-color: #fff;\n\tmargin-bottom: 20rpx;\n}\n\n.menu-item {\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.menu-item:last-child {\n\tborder-bottom: none;\n}\n\n.menu-icon {\n\tfont-size: 40rpx;\n\tmargin-right: 30rpx;\n}\n\n.menu-text {\n\tflex: 1;\n\tfont-size: 30rpx;\n\tcolor: #333;\n}\n\n.menu-count {\n\tbackground-color: #FF3B30;\n\tcolor: #fff;\n\tfont-size: 20rpx;\n\tpadding: 5rpx 15rpx;\n\tborder-radius: 15rpx;\n\tmargin-right: 20rpx;\n}\n\n.menu-arrow {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n\n/* 退出登录 */\n.logout-section {\n\tpadding: 30rpx;\n}\n\n.logout-btn {\n\twidth: 100%;\n\theight: 80rpx;\n\tbackground-color: #FF3B30;\n\tcolor: #fff;\n\tborder: none;\n\tborder-radius: 40rpx;\n\tfont-size: 28rpx;\n}\n\n/* 弹窗样式 */\n.modal {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tz-index: 1000;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.modal-content {\n\tbackground-color: #fff;\n\tborder-radius: 20rpx;\n\twidth: 90%;\n\tmax-height: 80vh;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.modal-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tpadding: 30rpx;\n\tborder-bottom: 1rpx solid #f0f0f0;\n}\n\n.modal-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.modal-close {\n\tfont-size: 40rpx;\n\tcolor: #999;\n\tpadding: 10rpx;\n}\n\n.modal-body {\n\tflex: 1;\n\tpadding: 20rpx;\n}\n\n.house-item {\n\tdisplay: flex;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 15rpx;\n\tpadding: 20rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.house-image {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tborder-radius: 10rpx;\n\tmargin-right: 20rpx;\n}\n\n.house-info {\n\tflex: 1;\n\tdisplay: flex;\n\tflex-direction: column;\n\tjustify-content: space-between;\n}\n\n.house-title {\n\tfont-size: 28rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n\n.house-price {\n\tfont-size: 26rpx;\n\tcolor: #FF3B30;\n\tfont-weight: bold;\n\tmargin-bottom: 10rpx;\n}\n\n.house-location {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n.house-status {\n\tmargin-top: 10rpx;\n}\n\n.status-text {\n\tfont-size: 22rpx;\n\tpadding: 5rpx 15rpx;\n\tborder-radius: 10rpx;\n}\n\n.status-pending {\n\tbackground-color: #FFF3E0;\n\tcolor: #FF9500;\n}\n\n.status-approved {\n\tbackground-color: #E8F5E8;\n\tcolor: #34C759;\n}\n\n.status-rejected {\n\tbackground-color: #FCE4EC;\n\tcolor: #FF3B30;\n}\n\n.empty-tip {\n\ttext-align: center;\n\tcolor: #999;\n\tfont-size: 28rpx;\n\tpadding: 60rpx 20rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=style&index=0&id=0be17cc6&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=style&index=0&id=0be17cc6&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754093677263\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}