<template>
	<view class="post-house-container">
		<form @submit="handleSubmit">
			<!-- 房源标题 -->
			<view class="form-section">
				<view class="section-title">房源标题 *</view>
				<view class="input-wrapper">
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入房源标题（5-100字）" 
						v-model="formData.title"
						:maxlength="100"
					/>
				</view>
			</view>

			<!-- 房源描述 -->
			<view class="form-section">
				<view class="section-title">房源描述</view>
				<view class="textarea-wrapper">
					<textarea 
						class="form-textarea" 
						placeholder="请详细描述房源情况，如装修情况、周边环境等" 
						v-model="formData.desc"
						:maxlength="1000"
						:show-confirm-bar="false"
					></textarea>
				</view>
			</view>

			<!-- 房源图片 -->
			<view class="form-section">
				<view class="section-title">房源图片</view>
				<view class="image-upload">
					<view class="image-list">
						<view class="image-item" v-for="(image, index) in formData.images" :key="index">
							<image :src="image" class="uploaded-image" mode="aspectFill"></image>
							<view class="image-delete" @click="removeImage(index)">✕</view>
						</view>
						<view class="image-add" @click="chooseImage" v-if="formData.images.length < 10">
							<text class="add-icon">📷</text>
							<text class="add-text">添加图片</text>
						</view>
					</view>
					<text class="image-tip">最多上传10张图片</text>
				</view>
			</view>

			<!-- 租金价格 -->
			<view class="form-section">
				<view class="section-title">租金价格 *</view>
				<view class="price-wrapper">
					<input 
						class="price-input" 
						type="number" 
						placeholder="请输入月租金" 
						v-model="formData.price"
					/>
					<text class="price-unit">元/月</text>
				</view>
			</view>

			<!-- 房型选择 -->
			<view class="form-section">
				<view class="section-title">房型 *</view>
				<view class="type-selector">
					<view 
						class="type-item" 
						:class="{ active: formData.type === type }"
						v-for="type in houseTypes" 
						:key="type"
						@click="selectType(type)"
					>
						{{ type }}
					</view>
				</view>
			</view>

			<!-- 房屋配置 -->
			<view class="form-section">
				<view class="section-title">房屋配置</view>
				<view class="config-selector">
					<view 
						class="config-item" 
						:class="{ active: formData.config.includes(config) }"
						v-for="config in houseConfigs" 
						:key="config"
						@click="toggleConfig(config)"
					>
						{{ config }}
					</view>
				</view>
			</view>

			<!-- 详细地址 -->
			<view class="form-section">
				<view class="section-title">详细地址 *</view>
				<view class="location-wrapper">
					<view class="location-input" @click="chooseLocation">
						<text class="location-text" v-if="formData.location.address">{{ formData.location.address }}</text>
						<text class="location-placeholder" v-else>点击选择位置</text>
						<text class="location-icon">📍</text>
					</view>
					<view class="location-actions">
						<button class="location-btn" @click="chooseLocation">选择位置</button>
						<button class="location-btn secondary" @click="showManualAddressInput">手动输入</button>
					</view>
				</view>
			</view>

			<!-- 联系方式 -->
			<view class="form-section">
				<view class="section-title">联系方式 *</view>
				<view class="contact-wrapper">
					<view class="contact-item">
						<text class="contact-label">手机号：</text>
						<input 
							class="contact-input" 
							type="number" 
							placeholder="请输入手机号" 
							v-model="formData.contact.phone"
							:maxlength="11"
						/>
					</view>
					<view class="contact-item">
						<text class="contact-label">微信号：</text>
						<input 
							class="contact-input" 
							type="text" 
							placeholder="请输入微信号（可选）" 
							v-model="formData.contact.wechat"
							:maxlength="50"
						/>
					</view>
				</view>
			</view>

			<!-- 提交按钮 -->
			<view class="submit-section">
				<button 
					class="submit-btn" 
					:class="{ disabled: !canSubmit }"
					:disabled="!canSubmit || loading"
					@click="handleSubmit"
				>
					{{ loading ? '发布中...' : '发布房源' }}
				</button>
				<text class="submit-tip">发布后需要管理员审核通过才能展示</text>
			</view>
		</form>
	</view>
</template>

<script>
import { houseManage, utils } from '@/utils/api.js';

export default {
	data() {
		return {
			formData: {
				title: '',
				desc: '',
				images: [],
				price: '',
				type: '',
				config: [],
				location: {
					latitude: '',
					longitude: '',
					address: ''
				},
				contact: {
					phone: '',
					wechat: ''
				}
			},
			loading: false,
			
			// 房型选项
			houseTypes: ['一居室', '二居室', '三居室', '四居室', '合租', '单间', '其他'],
			
			// 配置选项
			houseConfigs: [
				'空调', '洗衣机', '冰箱', '热水器', '宽带', '电视', 
				'沙发', '床', '衣柜', '书桌', '微波炉', '燃气灶', 
				'油烟机', '独立卫生间', '阳台', '停车位'
			]
		}
	},
	
	computed: {
		canSubmit() {
			return this.formData.title.trim().length >= 5 &&
				   this.formData.price &&
				   this.formData.type &&
				   this.formData.location.address &&
				   (this.formData.contact.phone || this.formData.contact.wechat);
		}
	},
	
	onLoad() {
		// 检查登录状态
		if (!utils.isLoggedIn()) {
			uni.showModal({
				title: '提示',
				content: '请先登录后再发布房源',
				success: (res) => {
					if (res.confirm) {
						utils.goToLogin();
					} else {
						uni.switchTab({
							url: '/pages/index/index'
						});
					}
				}
			});
		}
	},
	
	methods: {
		// 选择图片
		chooseImage() {
			const remainCount = 10 - this.formData.images.length;
			
			uni.chooseImage({
				count: remainCount,
				sizeType: ['compressed'],
				sourceType: ['album', 'camera'],
				success: (res) => {
					// 这里应该上传到云存储，暂时使用本地路径
					this.formData.images = [...this.formData.images, ...res.tempFilePaths];
				},
				fail: () => {
					utils.showError('选择图片失败');
				}
			});
		},
		
		// 删除图片
		removeImage(index) {
			this.formData.images.splice(index, 1);
		},
		
		// 选择房型
		selectType(type) {
			this.formData.type = type;
		},
		
		// 切换配置
		toggleConfig(config) {
			const index = this.formData.config.indexOf(config);
			if (index > -1) {
				this.formData.config.splice(index, 1);
			} else {
				this.formData.config.push(config);
			}
		},
		
		// 选择位置
		chooseLocation() {
			// 先检查位置权限
			uni.getSetting({
				success: (res) => {
					if (res.authSetting['scope.userLocation'] === false) {
						// 用户拒绝了位置权限，引导用户去设置
						uni.showModal({
							title: '位置权限',
							content: '需要获取您的位置信息来选择房源地址，请在设置中开启位置权限',
							confirmText: '去设置',
							success: (modalRes) => {
								if (modalRes.confirm) {
									uni.openSetting();
								}
							}
						});
						return;
					}

					// 有权限或未询问过，直接选择位置
					uni.chooseLocation({
						success: (res) => {
							this.formData.location = {
								latitude: res.latitude,
								longitude: res.longitude,
								address: res.address || res.name || '未知地址'
							};
							uni.showToast({
								title: '位置选择成功',
								icon: 'success'
							});
						},
						fail: (err) => {
							console.error('选择位置失败:', err);
							if (err.errMsg.includes('cancel')) {
								// 用户取消选择
								return;
							} else if (err.errMsg.includes('auth')) {
								// 权限问题
								uni.showModal({
									title: '位置权限',
									content: '需要获取您的位置信息来选择房源地址，请授权后重试',
									confirmText: '重新授权',
									success: (modalRes) => {
										if (modalRes.confirm) {
											this.chooseLocation();
										}
									}
								});
							} else {
								// 其他错误，提供手动输入选项
								uni.showModal({
									title: '获取位置失败',
									content: '无法获取位置信息，是否手动输入地址？',
									confirmText: '手动输入',
									success: (modalRes) => {
										if (modalRes.confirm) {
											this.showManualAddressInput();
										}
									}
								});
							}
						}
					});
				},
				fail: () => {
					uni.showToast({
						title: '获取设置失败',
						icon: 'none'
					});
				}
			});
		},

		// 显示手动输入地址的弹窗
		showManualAddressInput() {
			uni.showModal({
				title: '输入地址',
				editable: true,
				placeholderText: '请输入详细地址',
				success: (res) => {
					if (res.confirm && res.content.trim()) {
						this.formData.location = {
							latitude: '',
							longitude: '',
							address: res.content.trim()
						};
						uni.showToast({
							title: '地址设置成功',
							icon: 'success'
						});
					}
				}
			});
		},
		
		// 提交表单
		async handleSubmit() {
			if (!this.canSubmit) {
				utils.showError('请填写完整信息');
				return;
			}
			
			// 验证手机号格式
			if (this.formData.contact.phone && !/^1[3-9]\d{9}$/.test(this.formData.contact.phone)) {
				utils.showError('请输入正确的手机号');
				return;
			}
			
			this.loading = true;
			
			try {
				const houseData = {
					...this.formData,
					price: parseInt(this.formData.price)
				};
				
				const res = await houseManage.publishHouse(houseData);
				
				if (res.code === 200) {
					utils.showSuccess('发布成功，等待审核');
					
					// 清空表单
					this.resetForm();
					
					// 跳转到我的发布页面
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/my/my'
						});
					}, 1500);
				} else {
					utils.showError(res.message || '发布失败');
				}
			} catch (error) {
				console.error('发布房源失败:', error);
				utils.showError('网络错误');
			} finally {
				this.loading = false;
			}
		},
		
		// 重置表单
		resetForm() {
			this.formData = {
				title: '',
				desc: '',
				images: [],
				price: '',
				type: '',
				config: [],
				location: {
					latitude: '',
					longitude: '',
					address: ''
				},
				contact: {
					phone: '',
					wechat: ''
				}
			};
		}
	}
}
</script>

<style scoped>
.post-house-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 20rpx 30rpx 40rpx;
}

/* 表单区块样式 */
.form-section {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

/* 输入框样式 */
.input-wrapper {
	border: 2rpx solid #eee;
	border-radius: 15rpx;
	padding: 0 20rpx;
}

.form-input {
	height: 80rpx;
	font-size: 28rpx;
	color: #333;
	width: 100%;
}

.textarea-wrapper {
	border: 2rpx solid #eee;
	border-radius: 15rpx;
	padding: 20rpx;
}

.form-textarea {
	width: 100%;
	min-height: 200rpx;
	font-size: 28rpx;
	color: #333;
}

/* 图片上传样式 */
.image-upload {
	
}

.image-list {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
	margin-bottom: 20rpx;
}

.image-item {
	position: relative;
	aspect-ratio: 1;
	border-radius: 15rpx;
	overflow: hidden;
}

.uploaded-image {
	width: 100%;
	height: 100%;
}

.image-delete {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	width: 40rpx;
	height: 40rpx;
	background-color: rgba(0, 0, 0, 0.6);
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
}

.image-add {
	aspect-ratio: 1;
	border: 2rpx dashed #ddd;
	border-radius: 15rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: #999;
}

.add-icon {
	font-size: 48rpx;
	margin-bottom: 10rpx;
}

.add-text {
	font-size: 24rpx;
}

.image-tip {
	font-size: 24rpx;
	color: #999;
}

/* 价格输入样式 */
.price-wrapper {
	display: flex;
	align-items: center;
	border: 2rpx solid #eee;
	border-radius: 15rpx;
	padding: 0 20rpx;
}

.price-input {
	flex: 1;
	height: 80rpx;
	font-size: 28rpx;
	color: #333;
}

.price-unit {
	font-size: 28rpx;
	color: #666;
}

/* 房型选择样式 */
.type-selector {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
}

.type-item {
	padding: 20rpx;
	text-align: center;
	background-color: #f8f9fa;
	border-radius: 15rpx;
	font-size: 26rpx;
	color: #666;
}

.type-item.active {
	background-color: #007AFF;
	color: #fff;
}

/* 配置选择样式 */
.config-selector {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 15rpx;
}

.config-item {
	padding: 15rpx;
	text-align: center;
	background-color: #f8f9fa;
	border-radius: 15rpx;
	font-size: 24rpx;
	color: #666;
}

.config-item.active {
	background-color: #E3F2FD;
	color: #007AFF;
}

/* 位置选择样式 */
.location-wrapper {
	
}

.location-input {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border: 2rpx solid #eee;
	border-radius: 15rpx;
	padding: 20rpx;
	min-height: 80rpx;
}

.location-text {
	font-size: 28rpx;
	color: #333;
}

.location-placeholder {
	font-size: 28rpx;
	color: #999;
}

.location-icon {
	font-size: 32rpx;
}

.location-actions {
	display: flex;
	gap: 20rpx;
	margin-top: 20rpx;
}

.location-btn {
	flex: 1;
	height: 70rpx;
	border: none;
	border-radius: 35rpx;
	font-size: 26rpx;
	background-color: #007AFF;
	color: #fff;
}

.location-btn.secondary {
	background-color: #f8f9fa;
	color: #666;
}

/* 联系方式样式 */
.contact-wrapper {
	
}

.contact-item {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.contact-item:last-child {
	margin-bottom: 0;
}

.contact-label {
	width: 120rpx;
	font-size: 28rpx;
	color: #333;
}

.contact-input {
	flex: 1;
	height: 80rpx;
	border: 2rpx solid #eee;
	border-radius: 15rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
}

/* 提交按钮样式 */
.submit-section {
	text-align: center;
	margin-top: 40rpx;
}

.submit-btn {
	width: 100%;
	height: 100rpx;
	background-color: #007AFF;
	color: #fff;
	border: none;
	border-radius: 50rpx;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}

.submit-btn.disabled {
	background-color: #ccc;
}

.submit-tip {
	font-size: 24rpx;
	color: #999;
	display: block;
}
</style>
