{"version": 3, "sources": ["uni-app:///pages/userRegister/userRegister.vue", "webpack:///D:/web/project/前端11/pages/userRegister/userRegister.vue?965e", "webpack:///D:/web/project/前端11/pages/userRegister/userRegister.vue?5b23", "uni-app:///main.js", "webpack:///D:/web/project/前端11/pages/userRegister/userRegister.vue?47ce", "webpack:///D:/web/project/前端11/pages/userRegister/userRegister.vue?6890", "webpack:///D:/web/project/前端11/pages/userRegister/userRegister.vue?c545", "webpack:///D:/web/project/前端11/pages/userRegister/userRegister.vue?fcb9"], "names": ["data", "formData", "username", "nickname", "phone", "password", "confirmPassword", "showPassword", "showConfirmPassword", "loading", "computed", "isValidUsername", "isValidPhone", "isPasswordMatch", "canSubmit", "methods", "togglePassword", "toggleConfirmPassword", "handleRegister", "uni", "title", "icon", "registerData", "uniCloud", "name", "action", "res", "userData", "userId", "setTimeout", "url", "console", "goToLogin", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAoGA;EACAA;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA,oDACA,sCACA,wBACA,yBACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAGAC;kBACApB;kBACAG;gBACA,GAEA;gBACA;kBACAiB;gBACA;gBACA;kBACAA;gBACA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAxB;oBACAyB;oBACAzB;kBACA;gBACA;cAAA;gBANA0B;gBAQA;kBACA;kBACAC;kBACAR;kBACAA;oBACAS;oBACA1B;oBACAC;kBACA;kBAEAgB;oBACAC;oBACAC;kBACA;;kBAEA;kBACAQ;oBACAV;sBACAW;oBACA;kBACA;gBAEA;kBACAX;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAU;gBACAZ;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAW;MACAb;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1OA;AAAA;AAAA;AAAA;AAA87B,CAAgB,w5BAAG,EAAC,C;;;;;;;;;;;ACAl9B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAE2D;AAC3D;AACA;AAHA;AACAc,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACqC;;;AAGhG;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAuoB,CAAgB,qoBAAG,EAAC,C", "file": "pages/userRegister/userRegister.js", "sourcesContent": ["<template>\n\t<view class=\"register-container\">\n\t\t<view class=\"register-header\">\n\t\t\t<view class=\"logo-placeholder\">🏠</view>\n\t\t\t<text class=\"app-name\">租房小助手</text>\n\t\t\t<text class=\"welcome-text\">创建新账号</text>\n\t\t</view>\n\n\t\t<view class=\"register-form\">\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t\t<text class=\"input-icon\">👤</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\tplaceholder=\"请输入用户名（3-20位字符）\" \n\t\t\t\t\t\tv-model=\"formData.username\"\n\t\t\t\t\t\t:maxlength=\"20\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"input-tip\" v-if=\"formData.username && !isValidUsername\">用户名只能包含字母、数字和下划线</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t\t<text class=\"input-icon\">📝</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\tplaceholder=\"请输入昵称（可选）\" \n\t\t\t\t\t\tv-model=\"formData.nickname\"\n\t\t\t\t\t\t:maxlength=\"50\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t\t<text class=\"input-icon\">📱</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\ttype=\"number\" \n\t\t\t\t\t\tplaceholder=\"请输入手机号（可选）\" \n\t\t\t\t\t\tv-model=\"formData.phone\"\n\t\t\t\t\t\t:maxlength=\"11\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"input-tip\" v-if=\"formData.phone && !isValidPhone\">请输入正确的手机号</text>\n\t\t\t</view>\n\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t\t<text class=\"input-icon\">🔒</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t:type=\"showPassword ? 'text' : 'password'\" \n\t\t\t\t\t\tplaceholder=\"请输入密码（至少6位）\" \n\t\t\t\t\t\tv-model=\"formData.password\"\n\t\t\t\t\t\t:maxlength=\"20\"\n\t\t\t\t\t/>\n\t\t\t\t\t<text class=\"password-toggle\" @click=\"togglePassword\">\n\t\t\t\t\t\t{{ showPassword ? '👁️' : '🙈' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t\t<text class=\"input-icon\">🔐</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t:type=\"showConfirmPassword ? 'text' : 'password'\" \n\t\t\t\t\t\tplaceholder=\"请确认密码\" \n\t\t\t\t\t\tv-model=\"formData.confirmPassword\"\n\t\t\t\t\t\t:maxlength=\"20\"\n\t\t\t\t\t/>\n\t\t\t\t\t<text class=\"password-toggle\" @click=\"toggleConfirmPassword\">\n\t\t\t\t\t\t{{ showConfirmPassword ? '👁️' : '🙈' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<text class=\"input-tip error\" v-if=\"formData.confirmPassword && !isPasswordMatch\">两次密码输入不一致</text>\n\t\t\t</view>\n\n\t\t\t<button \n\t\t\t\tclass=\"register-btn\" \n\t\t\t\t:class=\"{ 'btn-disabled': !canSubmit }\"\n\t\t\t\t:disabled=\"!canSubmit || loading\"\n\t\t\t\t@click=\"handleRegister\"\n\t\t\t>\n\t\t\t\t{{ loading ? '注册中...' : '注册' }}\n\t\t\t</button>\n\n\t\t\t<view class=\"form-footer\">\n\t\t\t\t<text class=\"login-link\" @click=\"goToLogin\">已有账号？立即登录</text>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tformData: {\n\t\t\t\tusername: '',\n\t\t\t\tnickname: '',\n\t\t\t\tphone: '',\n\t\t\t\tpassword: '',\n\t\t\t\tconfirmPassword: ''\n\t\t\t},\n\t\t\tshowPassword: false,\n\t\t\tshowConfirmPassword: false,\n\t\t\tloading: false\n\t\t}\n\t},\n\tcomputed: {\n\t\t// 验证用户名格式\n\t\tisValidUsername() {\n\t\t\tconst username = this.formData.username.trim();\n\t\t\tif (!username) return true;\n\t\t\treturn /^[a-zA-Z0-9_]{3,20}$/.test(username);\n\t\t},\n\n\t\t// 验证手机号格式\n\t\tisValidPhone() {\n\t\t\tconst phone = this.formData.phone.trim();\n\t\t\tif (!phone) return true;\n\t\t\treturn /^1[3-9]\\d{9}$/.test(phone);\n\t\t},\n\n\t\t// 验证密码是否匹配\n\t\tisPasswordMatch() {\n\t\t\tif (!this.formData.confirmPassword) return true;\n\t\t\treturn this.formData.password === this.formData.confirmPassword;\n\t\t},\n\n\t\t// 是否可以提交\n\t\tcanSubmit() {\n\t\t\treturn this.formData.username.trim().length >= 3 &&\n\t\t\t\t   this.formData.password.length >= 6 &&\n\t\t\t\t   this.isValidUsername &&\n\t\t\t\t   this.isPasswordMatch &&\n\t\t\t\t   (!this.formData.phone || this.isValidPhone);\n\t\t}\n\t},\n\tmethods: {\n\t\t// 切换密码显示状态\n\t\ttogglePassword() {\n\t\t\tthis.showPassword = !this.showPassword;\n\t\t},\n\n\t\t// 切换确认密码显示状态\n\t\ttoggleConfirmPassword() {\n\t\t\tthis.showConfirmPassword = !this.showConfirmPassword;\n\t\t},\n\n\t\t// 处理注册\n\t\tasync handleRegister() {\n\t\t\tif (!this.canSubmit) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请检查输入信息',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.loading = true;\n\n\t\t\ttry {\n\t\t\t\tconst registerData = {\n\t\t\t\t\tusername: this.formData.username.trim(),\n\t\t\t\t\tpassword: this.formData.password.trim()\n\t\t\t\t};\n\n\t\t\t\t// 添加可选字段\n\t\t\t\tif (this.formData.nickname.trim()) {\n\t\t\t\t\tregisterData.nickname = this.formData.nickname.trim();\n\t\t\t\t}\n\t\t\t\tif (this.formData.phone.trim()) {\n\t\t\t\t\tregisterData.phone = this.formData.phone.trim();\n\t\t\t\t}\n\n\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\tname: 'user-auth',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: 'register',\n\t\t\t\t\t\tdata: registerData\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tif (res.result.code === 200) {\n\t\t\t\t\t// 保存用户信息和token\n\t\t\t\t\tconst userData = res.result.data;\n\t\t\t\t\tuni.setStorageSync('userToken', userData.token);\n\t\t\t\t\tuni.setStorageSync('userInfo', {\n\t\t\t\t\t\tuserId: userData.userId,\n\t\t\t\t\t\tusername: userData.username,\n\t\t\t\t\t\tnickname: userData.nickname\n\t\t\t\t\t});\n\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '注册成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\n\t\t\t\t\t// 延迟跳转到首页\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t\t});\n\t\t\t\t\t}, 1500);\n\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.result.message || '注册失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('注册失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\n\t\t// 跳转到登录页面\n\t\tgoToLogin() {\n\t\t\tuni.navigateBack();\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.register-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tpadding: 80rpx 60rpx 60rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.register-header {\n\ttext-align: center;\n\tmargin-bottom: 80rpx;\n}\n\n.logo-placeholder {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tmargin-bottom: 30rpx;\n\tfont-size: 80rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.app-name {\n\tdisplay: block;\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\tcolor: #fff;\n\tmargin-bottom: 20rpx;\n}\n\n.welcome-text {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tcolor: rgba(255, 255, 255, 0.8);\n}\n\n.register-form {\n\tflex: 1;\n}\n\n.form-item {\n\tmargin-bottom: 40rpx;\n}\n\n.input-wrapper {\n\tposition: relative;\n\tbackground-color: rgba(255, 255, 255, 0.9);\n\tborder-radius: 50rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 0 30rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n}\n\n.input-icon {\n\tfont-size: 32rpx;\n\tmargin-right: 20rpx;\n\tcolor: #666;\n}\n\n.form-input {\n\tflex: 1;\n\theight: 100rpx;\n\tfont-size: 32rpx;\n\tcolor: #333;\n}\n\n.password-toggle {\n\tfont-size: 32rpx;\n\tcolor: #666;\n\tpadding: 10rpx;\n}\n\n.input-tip {\n\tfont-size: 24rpx;\n\tcolor: rgba(255, 255, 255, 0.8);\n\tmargin-top: 10rpx;\n\tmargin-left: 30rpx;\n}\n\n.input-tip.error {\n\tcolor: #FFD700;\n}\n\n.register-btn {\n\twidth: 100%;\n\theight: 100rpx;\n\tbackground: linear-gradient(45deg, #FF6B6B, #FF8E53);\n\tborder: none;\n\tborder-radius: 50rpx;\n\tcolor: #fff;\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tmargin-top: 40rpx;\n\tbox-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.3);\n}\n\n.register-btn:active {\n\ttransform: translateY(2rpx);\n}\n\n.btn-disabled {\n\tbackground: #ccc !important;\n\tbox-shadow: none !important;\n}\n\n.form-footer {\n\ttext-align: center;\n\tmargin-top: 60rpx;\n}\n\n.login-link {\n\tcolor: rgba(255, 255, 255, 0.9);\n\tfont-size: 28rpx;\n\ttext-decoration: underline;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userRegister.vue?vue&type=style&index=0&id=5723e274&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userRegister.vue?vue&type=style&index=0&id=5723e274&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754093677260\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/userRegister/userRegister.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./userRegister.vue?vue&type=template&id=5723e274&scoped=true&\"\nvar renderjs\nimport script from \"./userRegister.vue?vue&type=script&lang=js&\"\nexport * from \"./userRegister.vue?vue&type=script&lang=js&\"\nimport style0 from \"./userRegister.vue?vue&type=style&index=0&id=5723e274&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5723e274\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/userRegister/userRegister.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userRegister.vue?vue&type=template&id=5723e274&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userRegister.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userRegister.vue?vue&type=script&lang=js&\""], "sourceRoot": ""}