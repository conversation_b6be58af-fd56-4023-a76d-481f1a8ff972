
.register-container.data-v-5723e274 {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 80rpx 60rpx 60rpx;
	display: flex;
	flex-direction: column;
}
.register-header.data-v-5723e274 {
	text-align: center;
	margin-bottom: 80rpx;
}
.logo-placeholder.data-v-5723e274 {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 30rpx;
	font-size: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}
.app-name.data-v-5723e274 {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #fff;
	margin-bottom: 20rpx;
}
.welcome-text.data-v-5723e274 {
	display: block;
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
}
.register-form.data-v-5723e274 {
	flex: 1;
}
.form-item.data-v-5723e274 {
	margin-bottom: 40rpx;
}
.input-wrapper.data-v-5723e274 {
	position: relative;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}
.input-icon.data-v-5723e274 {
	font-size: 32rpx;
	margin-right: 20rpx;
	color: #666;
}
.form-input.data-v-5723e274 {
	flex: 1;
	height: 100rpx;
	font-size: 32rpx;
	color: #333;
}
.password-toggle.data-v-5723e274 {
	font-size: 32rpx;
	color: #666;
	padding: 10rpx;
}
.input-tip.data-v-5723e274 {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	margin-top: 10rpx;
	margin-left: 30rpx;
}
.input-tip.error.data-v-5723e274 {
	color: #FFD700;
}
.register-btn.data-v-5723e274 {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(45deg, #FF6B6B, #FF8E53);
	border: none;
	border-radius: 50rpx;
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	margin-top: 40rpx;
	box-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.3);
}
.register-btn.data-v-5723e274:active {
	-webkit-transform: translateY(2rpx);
	        transform: translateY(2rpx);
}
.btn-disabled.data-v-5723e274 {
	background: #ccc !important;
	box-shadow: none !important;
}
.form-footer.data-v-5723e274 {
	text-align: center;
	margin-top: 60rpx;
}
.login-link.data-v-5723e274 {
	color: rgba(255, 255, 255, 0.9);
	font-size: 28rpx;
	text-decoration: underline;
}

