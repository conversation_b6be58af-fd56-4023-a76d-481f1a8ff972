# 租房小助手 - UniApp 小程序

一个基于 UniApp + UniCloud 的房屋租赁信息小程序，提供房源发布、搜索、收藏等功能。

## 项目特色

- 🏠 **完整的房源管理** - 发布、编辑、删除、审核房源
- 🔍 **强大的搜索功能** - 关键词搜索、多条件筛选
- ❤️ **用户收藏系统** - 收藏喜欢的房源
- 👤 **用户认证系统** - 注册、登录、资料管理
- 📱 **响应式设计** - 适配各种屏幕尺寸
- ☁️ **云端数据存储** - 基于 UniCloud 云数据库
- 🛡️ **安全可靠** - 完善的权限控制和数据验证

## 技术栈

### 前端
- **UniApp** - 跨平台开发框架
- **Vue 2** - 渐进式 JavaScript 框架
- **UniCloud** - 云开发平台

### 后端
- **UniCloud 云函数** - 服务端逻辑
- **UniCloud 云数据库** - 数据存储
- **Node.js** - 云函数运行环境

## 项目结构

```
├── pages/                  # 页面文件
│   ├── home/              # 首页
│   ├── houseList/         # 房源列表页
│   ├── houseDetail/       # 房源详情页
│   ├── postHouse/         # 发布房源页
│   ├── my/                # 我的页面
│   ├── userLogin/         # 用户登录页
│   └── userRegister/      # 用户注册页
├── uniCloud-aliyun/       # 云开发相关
│   ├── cloudfunctions/    # 云函数
│   └── database/          # 数据库配置
├── utils/                 # 工具类
│   └── api.js            # API 接口封装
├── static/               # 静态资源
├── App.vue              # 应用入口
├── main.js              # 主入口文件
├── pages.json           # 页面配置
├── manifest.json        # 应用配置
└── uni.scss            # 全局样式
```

## 功能模块

### 1. 用户系统
- ✅ 用户注册（用户名、密码、昵称、手机号）
- ✅ 用户登录（用户名密码登录）
- ✅ 用户信息管理
- ✅ 密码修改
- ✅ 登录状态管理

### 2. 房源管理
- ✅ 房源发布（标题、描述、图片、价格、房型、配置、位置、联系方式）
- ✅ 房源列表展示（分页、筛选、搜索）
- ✅ 房源详情查看
- ✅ 房源编辑和删除
- ✅ 房源状态管理（待审核、已通过、已驳回）

### 3. 搜索筛选
- ✅ 关键词搜索（标题、描述、地址）
- ✅ 价格范围筛选
- ✅ 房型筛选
- ✅ 地区筛选
- ✅ 排序功能

### 4. 收藏功能
- ✅ 添加/取消收藏
- ✅ 收藏列表管理
- ✅ 收藏状态检查

### 5. 管理后台
- ✅ 管理员登录
- ✅ 房源审核管理
- ✅ 用户管理（封禁/解封）
- ✅ 数据统计
- ✅ 系统设置

## 云函数说明

### user-auth（用户认证）
- 用户注册、登录
- 用户信息获取和更新
- 密码修改

### house-manage（房源管理）
- 房源发布、编辑、删除
- 房源列表获取
- 房源搜索

### user-favorites（用户收藏）
- 添加/取消收藏
- 获取收藏列表
- 检查收藏状态

### admin-manage（管理员功能）
- 管理员登录
- 房源审核
- 用户管理
- 数据统计

## 数据库设计

### user 集合（用户信息）
```javascript
{
  _id: String,           // 用户ID
  username: String,      // 用户名（唯一）
  password: String,      // 加密密码
  nickname: String,      // 昵称
  avatar: String,        // 头像URL
  phone: String,         // 手机号
  is_banned: Boolean,    // 是否被封禁
  created_at: Date,      // 注册时间
  favorites: Array       // 收藏的房源ID数组
}
```

### house 集合（房源信息）
```javascript
{
  _id: String,           // 房源ID
  title: String,         // 房源标题
  desc: String,          // 房源描述
  images: Array,         // 房源图片URL数组
  location: Object,      // 位置信息（经纬度+地址）
  price: Number,         // 月租金
  type: String,          // 房型
  config: Array,         // 房屋配置
  contact: Object,       // 联系方式
  owner_id: String,      // 发布用户ID
  status: String,        // 审核状态
  created_at: Date,      // 创建时间
  updated_at: Date       // 更新时间
}
```

## 快速开始

### 1. 环境准备
- 安装 HBuilderX
- 注册 DCloud 开发者账号
- 创建 UniCloud 服务空间

### 2. 项目配置
1. 在 HBuilderX 中打开项目
2. 关联 UniCloud 服务空间
3. 上传云函数到服务空间
4. 在云数据库中创建对应的集合

### 3. 运行项目
1. 在 HBuilderX 中选择运行到微信小程序
2. 或运行到浏览器进行调试

## 默认账号

### 测试用户
- 用户名：testuser
- 密码：123456

### 管理员账号
- 用户名：admin
- 密码：admin123

## 开发说明

### API 调用
项目中所有的云函数调用都封装在 `utils/api.js` 中，使用方式：

```javascript
import { userAuth, houseManage, userFavorites } from '@/utils/api.js';

// 用户登录
const res = await userAuth.login({ username, password });

// 获取房源列表
const res = await houseManage.getHouseList({ page: 1, pageSize: 10 });

// 添加收藏
const res = await userFavorites.addFavorite(houseId);
```

### 样式规范
- 使用 rpx 作为尺寸单位
- 遵循统一的颜色规范（定义在 uni.scss 中）
- 组件化开发，复用性强

### 错误处理
- 统一的错误提示机制
- 网络异常处理
- 用户友好的错误信息

## 待优化功能

- [ ] 图片上传到云存储
- [ ] 地图找房功能
- [ ] 消息推送
- [ ] 房源推荐算法
- [ ] 多语言支持
- [ ] 主题切换

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。
