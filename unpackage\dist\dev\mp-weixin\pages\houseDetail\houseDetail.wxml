<view class="house-detail-container data-v-6992ea2a"><view class="image-section data-v-6992ea2a"><swiper class="image-swiper data-v-6992ea2a" indicator-dots="{{true}}" autoplay="{{false}}" duration="{{300}}"><block wx:for="{{houseData.images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><swiper-item class="data-v-6992ea2a"><image class="house-image data-v-6992ea2a" src="{{image}}" mode="aspectFill" data-event-opts="{{[['tap',[['previewImage',[index]]]]]}}" bindtap="__e"></image></swiper-item></block></swiper><view class="image-count data-v-6992ea2a">{{currentImageIndex+1+"/"+$root.g0}}</view></view><view class="info-section data-v-6992ea2a"><view class="price-section data-v-6992ea2a"><text class="price data-v-6992ea2a">{{"¥"+houseData.price}}</text><text class="price-unit data-v-6992ea2a">/月</text><view data-event-opts="{{[['tap',[['toggleFavorite',['$event']]]]]}}" class="{{['favorite-btn','data-v-6992ea2a',(isFavorited)?'active':'']}}" bindtap="__e"><text class="favorite-icon data-v-6992ea2a">{{isFavorited?'❤️':'🤍'}}</text></view></view><text class="house-title data-v-6992ea2a">{{houseData.title}}</text><view class="house-tags data-v-6992ea2a"><text class="house-tag primary data-v-6992ea2a">{{houseData.type}}</text><block wx:for="{{houseData.config}}" wx:for-item="config" wx:for-index="__i0__" wx:key="*this"><text class="house-tag data-v-6992ea2a">{{config}}</text></block></view><view class="location-section data-v-6992ea2a"><text class="location-icon data-v-6992ea2a">📍</text><text class="location-text data-v-6992ea2a">{{houseData.location.address}}</text></view></view><view class="desc-section data-v-6992ea2a"><view class="section-title data-v-6992ea2a">房源描述</view><text class="desc-text data-v-6992ea2a">{{houseData.desc||'暂无详细描述'}}</text></view><block wx:if="{{$root.g1}}"><view class="config-section data-v-6992ea2a"><view class="section-title data-v-6992ea2a">房屋配置</view><view class="config-grid data-v-6992ea2a"><block wx:for="{{$root.l0}}" wx:for-item="config" wx:for-index="__i1__" wx:key="$orig"><view class="config-item data-v-6992ea2a"><text class="config-icon data-v-6992ea2a">{{config.m0}}</text><text class="config-text data-v-6992ea2a">{{config.$orig}}</text></view></block></view></view></block><view class="contact-section data-v-6992ea2a"><view class="section-title data-v-6992ea2a">联系方式</view><block wx:if="{{houseData.contact.phone}}"><view class="contact-item data-v-6992ea2a"><text class="contact-icon data-v-6992ea2a">📞</text><text class="contact-text data-v-6992ea2a">{{houseData.contact.phone}}</text><button data-event-opts="{{[['tap',[['makePhoneCall',['$event']]]]]}}" class="contact-btn data-v-6992ea2a" bindtap="__e">拨打电话</button></view></block><block wx:if="{{houseData.contact.wechat}}"><view class="contact-item data-v-6992ea2a"><text class="contact-icon data-v-6992ea2a">💬</text><text class="contact-text data-v-6992ea2a">{{houseData.contact.wechat}}</text><button data-event-opts="{{[['tap',[['copyWechat',['$event']]]]]}}" class="contact-btn data-v-6992ea2a" bindtap="__e">复制微信</button></view></block></view><view class="publish-section data-v-6992ea2a"><view class="section-title data-v-6992ea2a">发布信息</view><text class="publish-time data-v-6992ea2a">{{"发布时间："+$root.m1}}</text><block wx:if="{{houseData.updated_at!==houseData.created_at}}"><text class="update-time data-v-6992ea2a">{{'更新时间：'+$root.m2+''}}</text></block></view><view class="bottom-actions data-v-6992ea2a"><button data-event-opts="{{[['tap',[['toggleFavorite',['$event']]]]]}}" class="action-btn secondary data-v-6992ea2a" bindtap="__e">{{''+(isFavorited?'取消收藏':'收藏房源')+''}}</button><button data-event-opts="{{[['tap',[['contactOwner',['$event']]]]]}}" class="action-btn primary data-v-6992ea2a" bindtap="__e">联系房东</button></view><block wx:if="{{loading}}"><view class="loading-section data-v-6992ea2a"><text class="loading-text data-v-6992ea2a">加载中...</text></view></block></view>