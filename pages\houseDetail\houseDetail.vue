<template>
	<view class="house-detail-container">
		<!-- 图片轮播 -->
		<view class="image-section">
			<swiper class="image-swiper" :indicator-dots="true" :autoplay="false" :duration="300">
				<swiper-item v-for="(image, index) in houseData.images" :key="index">
					<image :src="image" class="house-image" mode="aspectFill" @click="previewImage(index)"></image>
				</swiper-item>
			</swiper>
			<view class="image-count">{{ currentImageIndex + 1 }}/{{ houseData.images.length }}</view>
		</view>

		<!-- 基本信息 -->
		<view class="info-section">
			<view class="price-section">
				<text class="price">¥{{ houseData.price }}</text>
				<text class="price-unit">/月</text>
				<view class="favorite-btn" :class="{ active: isFavorited }" @click="toggleFavorite">
					<text class="favorite-icon">{{ isFavorited ? '❤️' : '🤍' }}</text>
				</view>
			</view>
			
			<text class="house-title">{{ houseData.title }}</text>
			
			<view class="house-tags">
				<text class="house-tag primary">{{ houseData.type }}</text>
				<text class="house-tag" v-for="config in houseData.config" :key="config">{{ config }}</text>
			</view>
			
			<view class="location-section">
				<text class="location-icon">📍</text>
				<text class="location-text">{{ houseData.location.address }}</text>
			</view>
		</view>

		<!-- 详细描述 -->
		<view class="desc-section">
			<view class="section-title">房源描述</view>
			<text class="desc-text">{{ houseData.desc || '暂无详细描述' }}</text>
		</view>

		<!-- 房屋配置 -->
		<view class="config-section" v-if="houseData.config && houseData.config.length > 0">
			<view class="section-title">房屋配置</view>
			<view class="config-grid">
				<view class="config-item" v-for="config in houseData.config" :key="config">
					<text class="config-icon">{{ getConfigIcon(config) }}</text>
					<text class="config-text">{{ config }}</text>
				</view>
			</view>
		</view>

		<!-- 联系房东 -->
		<view class="contact-section">
			<view class="section-title">联系方式</view>
			<view class="contact-item" v-if="houseData.contact.phone">
				<text class="contact-icon">📞</text>
				<text class="contact-text">{{ houseData.contact.phone }}</text>
				<button class="contact-btn" @click="makePhoneCall">拨打电话</button>
			</view>
			<view class="contact-item" v-if="houseData.contact.wechat">
				<text class="contact-icon">💬</text>
				<text class="contact-text">{{ houseData.contact.wechat }}</text>
				<button class="contact-btn" @click="copyWechat">复制微信</button>
			</view>
		</view>

		<!-- 发布信息 -->
		<view class="publish-section">
			<view class="section-title">发布信息</view>
			<text class="publish-time">发布时间：{{ formatTime(houseData.created_at) }}</text>
			<text class="update-time" v-if="houseData.updated_at !== houseData.created_at">
				更新时间：{{ formatTime(houseData.updated_at) }}
			</text>
		</view>

		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<button class="action-btn secondary" @click="toggleFavorite">
				{{ isFavorited ? '取消收藏' : '收藏房源' }}
			</button>
			<button class="action-btn primary" @click="contactOwner">
				联系房东
			</button>
		</view>

		<!-- 加载状态 -->
		<view class="loading-section" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
import { houseManage, userFavorites, utils } from '@/utils/api.js';

export default {
	data() {
		return {
			houseId: '',
			houseData: {
				images: [],
				title: '',
				desc: '',
				price: 0,
				type: '',
				config: [],
				location: { address: '' },
				contact: { phone: '', wechat: '' },
				created_at: '',
				updated_at: ''
			},
			currentImageIndex: 0,
			isFavorited: false,
			loading: true
		}
	},
	
	onLoad(options) {
		if (options.id) {
			this.houseId = options.id;
			this.loadHouseDetail();
			this.checkFavoriteStatus();
		} else {
			uni.showToast({
				title: '房源ID不存在',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		}
	},
	
	methods: {
		// 加载房源详情
		async loadHouseDetail() {
			this.loading = true;
			
			try {
				const res = await houseManage.getHouseDetail(this.houseId);
				
				if (res.code === 200) {
					this.houseData = res.data;
					// 如果没有图片，添加默认图片
					if (!this.houseData.images || this.houseData.images.length === 0) {
						this.houseData.images = ['https://via.placeholder.com/750x500/CCCCCC/FFFFFF?text=暂无图片'];
					}
				} else {
					utils.showError(res.message || '加载失败');
					setTimeout(() => {
						uni.navigateBack();
					}, 1500);
				}
			} catch (error) {
				console.error('加载房源详情失败:', error);
				utils.showError('网络错误');
			} finally {
				this.loading = false;
			}
		},
		
		// 检查收藏状态
		async checkFavoriteStatus() {
			if (!utils.isLoggedIn()) return;
			
			try {
				const res = await userFavorites.checkFavorite(this.houseId);
				if (res.code === 200) {
					this.isFavorited = res.data.isFavorited;
				}
			} catch (error) {
				console.error('检查收藏状态失败:', error);
			}
		},
		
		// 切换收藏状态
		async toggleFavorite() {
			if (!utils.isLoggedIn()) {
				uni.showModal({
					title: '提示',
					content: '请先登录后再收藏房源',
					success: (res) => {
						if (res.confirm) {
							utils.goToLogin();
						}
					}
				});
				return;
			}
			
			try {
				let res;
				if (this.isFavorited) {
					res = await userFavorites.removeFavorite(this.houseId);
				} else {
					res = await userFavorites.addFavorite(this.houseId);
				}
				
				if (res.code === 200) {
					this.isFavorited = !this.isFavorited;
					utils.showSuccess(res.message);
				} else {
					utils.showError(res.message);
				}
			} catch (error) {
				console.error('收藏操作失败:', error);
				utils.showError('操作失败');
			}
		},
		
		// 预览图片
		previewImage(index) {
			this.currentImageIndex = index;
			uni.previewImage({
				urls: this.houseData.images,
				current: index
			});
		},
		
		// 拨打电话
		makePhoneCall() {
			uni.makePhoneCall({
				phoneNumber: this.houseData.contact.phone,
				fail: () => {
					utils.showError('拨打电话失败');
				}
			});
		},
		
		// 复制微信号
		copyWechat() {
			uni.setClipboardData({
				data: this.houseData.contact.wechat,
				success: () => {
					utils.showSuccess('微信号已复制');
				},
				fail: () => {
					utils.showError('复制失败');
				}
			});
		},
		
		// 联系房东
		contactOwner() {
			const actions = [];
			
			if (this.houseData.contact.phone) {
				actions.push('拨打电话');
			}
			if (this.houseData.contact.wechat) {
				actions.push('复制微信');
			}
			
			if (actions.length === 0) {
				utils.showError('暂无联系方式');
				return;
			}
			
			if (actions.length === 1) {
				if (actions[0] === '拨打电话') {
					this.makePhoneCall();
				} else {
					this.copyWechat();
				}
				return;
			}
			
			uni.showActionSheet({
				itemList: actions,
				success: (res) => {
					if (actions[res.tapIndex] === '拨打电话') {
						this.makePhoneCall();
					} else {
						this.copyWechat();
					}
				}
			});
		},
		
		// 获取配置图标
		getConfigIcon(config) {
			const iconMap = {
				'空调': '❄️',
				'洗衣机': '👕',
				'冰箱': '🧊',
				'热水器': '🚿',
				'宽带': '📶',
				'电视': '📺',
				'沙发': '🛋️',
				'床': '🛏️',
				'衣柜': '👗',
				'书桌': '📚',
				'微波炉': '🔥',
				'燃气灶': '🔥',
				'油烟机': '💨',
				'独立卫生间': '🚽',
				'阳台': '🌿',
				'停车位': '🚗'
			};
			return iconMap[config] || '✅';
		},
		
		// 格式化时间
		formatTime(timestamp) {
			return utils.formatTime(timestamp);
		}
	}
}
</script>

<style scoped>
.house-detail-container {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 图片轮播样式 */
.image-section {
	position: relative;
	height: 500rpx;
}

.image-swiper {
	height: 100%;
}

.house-image {
	width: 100%;
	height: 100%;
}

.image-count {
	position: absolute;
	bottom: 20rpx;
	right: 20rpx;
	background-color: rgba(0, 0, 0, 0.6);
	color: #fff;
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

/* 基本信息样式 */
.info-section {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.price-section {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}

.price {
	font-size: 48rpx;
	font-weight: bold;
	color: #FF3B30;
}

.price-unit {
	font-size: 28rpx;
	color: #999;
	margin-left: 10rpx;
}

.favorite-btn {
	margin-left: auto;
	padding: 10rpx;
}

.favorite-icon {
	font-size: 48rpx;
}

.house-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.house-tags {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 20rpx;
}

.house-tag {
	background-color: #f0f0f0;
	color: #666;
	font-size: 24rpx;
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	margin-right: 15rpx;
	margin-bottom: 10rpx;
}

.house-tag.primary {
	background-color: #E3F2FD;
	color: #007AFF;
}

.location-section {
	display: flex;
	align-items: center;
}

.location-icon {
	font-size: 28rpx;
	margin-right: 10rpx;
}

.location-text {
	font-size: 28rpx;
	color: #666;
}

/* 描述样式 */
.desc-section {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.desc-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	display: block;
}

/* 配置样式 */
.config-section {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.config-grid {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 30rpx;
}

.config-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}

.config-icon {
	font-size: 40rpx;
	margin-bottom: 10rpx;
}

.config-text {
	font-size: 24rpx;
	color: #666;
}

/* 联系方式样式 */
.contact-section {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.contact-item {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}

.contact-item:last-child {
	border-bottom: none;
}

.contact-icon {
	font-size: 32rpx;
	margin-right: 20rpx;
}

.contact-text {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}

.contact-btn {
	background-color: #007AFF;
	color: #fff;
	border: none;
	border-radius: 30rpx;
	padding: 15rpx 30rpx;
	font-size: 24rpx;
}

/* 发布信息样式 */
.publish-section {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.publish-time,
.update-time {
	font-size: 26rpx;
	color: #999;
	display: block;
	margin-bottom: 10rpx;
}

/* 底部操作栏样式 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #eee;
	display: flex;
	gap: 20rpx;
	z-index: 100;
}

.action-btn {
	flex: 1;
	height: 80rpx;
	border: none;
	border-radius: 40rpx;
	font-size: 28rpx;
	font-weight: bold;
}

.action-btn.primary {
	background-color: #007AFF;
	color: #fff;
}

.action-btn.secondary {
	background-color: #f8f9fa;
	color: #666;
}

/* 加载状态样式 */
.loading-section {
	padding: 100rpx;
	text-align: center;
}

.loading-text {
	color: #999;
	font-size: 28rpx;
}
</style>
