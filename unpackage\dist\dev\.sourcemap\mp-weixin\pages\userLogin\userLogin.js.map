{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端11/pages/userLogin/userLogin.vue?a175", "webpack:///D:/web/project/前端11/pages/userLogin/userLogin.vue?65ad", "webpack:///D:/web/project/前端11/pages/userLogin/userLogin.vue?bd7f", "webpack:///D:/web/project/前端11/pages/userLogin/userLogin.vue?568a", "uni-app:///pages/userLogin/userLogin.vue", "webpack:///D:/web/project/前端11/pages/userLogin/userLogin.vue?6ae4", "webpack:///D:/web/project/前端11/pages/userLogin/userLogin.vue?5ea4"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formData", "username", "password", "showPassword", "loading", "computed", "canSubmit", "methods", "togglePassword", "handleLogin", "uni", "title", "icon", "uniCloud", "name", "action", "res", "userData", "userId", "nickname", "avatar", "phone", "setTimeout", "url", "console", "goToRegister", "onLoad", "content", "success"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACqC;;;AAG7F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAooB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6DxpB;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAGAC;kBACAC;kBACAf;oBACAgB;oBACAhB;sBACAE;sBACAC;oBACA;kBACA;gBACA;cAAA;gBATAc;gBAWA;kBACA;kBACAC;kBACAP;kBACAA;oBACAQ;oBACAjB;oBACAkB;oBACAC;oBACAC;kBACA;kBAEAX;oBACAC;oBACAC;kBACA;;kBAEA;kBACAU;oBACA;oBACA;oBACA;sBACAZ;oBACA;sBACAA;wBACAa;sBACA;oBACA;kBACA;gBAEA;kBACAb;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAY;gBACAd;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAa;MACAf;QACAa;MACA;IACA;EACA;EAEAG;IACA;IACA;IACA;MACAhB;QACAC;QACAgB;QACAC;UACA;YACA;YACAlB;YACAA;UACA;YACA;YACA;YACA;cACAA;YACA;cACAA;gBACAa;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7LA;AAAA;AAAA;AAAA;AAA27B,CAAgB,q5BAAG,EAAC,C;;;;;;;;;;;ACA/8B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/userLogin/userLogin.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/userLogin/userLogin.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./userLogin.vue?vue&type=template&id=7264ae84&scoped=true&\"\nvar renderjs\nimport script from \"./userLogin.vue?vue&type=script&lang=js&\"\nexport * from \"./userLogin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./userLogin.vue?vue&type=style&index=0&id=7264ae84&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7264ae84\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/userLogin/userLogin.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userLogin.vue?vue&type=template&id=7264ae84&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userLogin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userLogin.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"login-container\">\n\t\t<view class=\"login-header\">\n\t\t\t<view class=\"logo-placeholder\">🏠</view>\n\t\t\t<text class=\"app-name\">租房小助手</text>\n\t\t\t<text class=\"welcome-text\">欢迎回来</text>\n\t\t</view>\n\n\t\t<view class=\"login-form\">\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t\t<text class=\"input-icon\">👤</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\tplaceholder=\"请输入用户名\" \n\t\t\t\t\t\tv-model=\"formData.username\"\n\t\t\t\t\t\t:maxlength=\"20\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"form-item\">\n\t\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t\t<text class=\"input-icon\">🔒</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\t:type=\"showPassword ? 'text' : 'password'\" \n\t\t\t\t\t\tplaceholder=\"请输入密码\" \n\t\t\t\t\t\tv-model=\"formData.password\"\n\t\t\t\t\t\t:maxlength=\"20\"\n\t\t\t\t\t/>\n\t\t\t\t\t<text class=\"password-toggle\" @click=\"togglePassword\">\n\t\t\t\t\t\t{{ showPassword ? '👁️' : '🙈' }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<button \n\t\t\t\tclass=\"login-btn\" \n\t\t\t\t:class=\"{ 'btn-disabled': !canSubmit }\"\n\t\t\t\t:disabled=\"!canSubmit || loading\"\n\t\t\t\t@click=\"handleLogin\"\n\t\t\t>\n\t\t\t\t{{ loading ? '登录中...' : '登录' }}\n\t\t\t</button>\n\n\t\t\t<view class=\"form-footer\">\n\t\t\t\t<text class=\"register-link\" @click=\"goToRegister\">还没有账号？立即注册</text>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 快速登录提示 -->\n\t\t<view class=\"quick-login-tip\">\n\t\t\t<text class=\"tip-text\">测试账号：testuser</text>\n\t\t\t<text class=\"tip-text\">测试密码：123456</text>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tformData: {\n\t\t\t\tusername: '',\n\t\t\t\tpassword: ''\n\t\t\t},\n\t\t\tshowPassword: false,\n\t\t\tloading: false\n\t\t}\n\t},\n\tcomputed: {\n\t\tcanSubmit() {\n\t\t\treturn this.formData.username.trim() && this.formData.password.trim();\n\t\t}\n\t},\n\tmethods: {\n\t\t// 切换密码显示状态\n\t\ttogglePassword() {\n\t\t\tthis.showPassword = !this.showPassword;\n\t\t},\n\n\t\t// 处理登录\n\t\tasync handleLogin() {\n\t\t\tif (!this.canSubmit) {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '请填写完整信息',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tthis.loading = true;\n\n\t\t\ttry {\n\t\t\t\tconst res = await uniCloud.callFunction({\n\t\t\t\t\tname: 'user-auth',\n\t\t\t\t\tdata: {\n\t\t\t\t\t\taction: 'login',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tusername: this.formData.username.trim(),\n\t\t\t\t\t\t\tpassword: this.formData.password.trim()\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tif (res.result.code === 200) {\n\t\t\t\t\t// 保存用户信息和token\n\t\t\t\t\tconst userData = res.result.data;\n\t\t\t\t\tuni.setStorageSync('userToken', userData.token);\n\t\t\t\t\tuni.setStorageSync('userInfo', {\n\t\t\t\t\t\tuserId: userData.userId,\n\t\t\t\t\t\tusername: userData.username,\n\t\t\t\t\t\tnickname: userData.nickname,\n\t\t\t\t\t\tavatar: userData.avatar,\n\t\t\t\t\t\tphone: userData.phone\n\t\t\t\t\t});\n\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '登录成功',\n\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t});\n\n\t\t\t\t\t// 延迟跳转，让用户看到成功提示\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t// 检查是否有来源页面，有则返回，没有则跳转到首页\n\t\t\t\t\t\tconst pages = getCurrentPages();\n\t\t\t\t\t\tif (pages.length > 1) {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}, 1500);\n\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.result.message || '登录失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('登录失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\n\t\t// 跳转到注册页面\n\t\tgoToRegister() {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: '/pages/userRegister/userRegister'\n\t\t\t});\n\t\t}\n\t},\n\n\tonLoad() {\n\t\t// 检查是否已经登录\n\t\tconst token = uni.getStorageSync('userToken');\n\t\tif (token) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '提示',\n\t\t\t\tcontent: '您已经登录，是否重新登录？',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t// 清除登录信息\n\t\t\t\t\t\tuni.removeStorageSync('userToken');\n\t\t\t\t\t\tuni.removeStorageSync('userInfo');\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 返回上一页或首页\n\t\t\t\t\t\tconst pages = getCurrentPages();\n\t\t\t\t\t\tif (pages.length > 1) {\n\t\t\t\t\t\t\tuni.navigateBack();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.login-container {\n\tmin-height: 100vh;\n\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\tpadding: 100rpx 60rpx 60rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.login-header {\n\ttext-align: center;\n\tmargin-bottom: 100rpx;\n}\n\n.logo-placeholder {\n\twidth: 120rpx;\n\theight: 120rpx;\n\tmargin-bottom: 30rpx;\n\tfont-size: 80rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n}\n\n.app-name {\n\tdisplay: block;\n\tfont-size: 48rpx;\n\tfont-weight: bold;\n\tcolor: #fff;\n\tmargin-bottom: 20rpx;\n}\n\n.welcome-text {\n\tdisplay: block;\n\tfont-size: 28rpx;\n\tcolor: rgba(255, 255, 255, 0.8);\n}\n\n.login-form {\n\tflex: 1;\n}\n\n.form-item {\n\tmargin-bottom: 40rpx;\n}\n\n.input-wrapper {\n\tposition: relative;\n\tbackground-color: rgba(255, 255, 255, 0.9);\n\tborder-radius: 50rpx;\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 0 30rpx;\n\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n}\n\n.input-icon {\n\tfont-size: 32rpx;\n\tmargin-right: 20rpx;\n\tcolor: #666;\n}\n\n.form-input {\n\tflex: 1;\n\theight: 100rpx;\n\tfont-size: 32rpx;\n\tcolor: #333;\n}\n\n.password-toggle {\n\tfont-size: 32rpx;\n\tcolor: #666;\n\tpadding: 10rpx;\n}\n\n.login-btn {\n\twidth: 100%;\n\theight: 100rpx;\n\tbackground: linear-gradient(45deg, #FF6B6B, #FF8E53);\n\tborder: none;\n\tborder-radius: 50rpx;\n\tcolor: #fff;\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tmargin-top: 60rpx;\n\tbox-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.3);\n}\n\n.login-btn:active {\n\ttransform: translateY(2rpx);\n}\n\n.btn-disabled {\n\tbackground: #ccc !important;\n\tbox-shadow: none !important;\n}\n\n.form-footer {\n\ttext-align: center;\n\tmargin-top: 60rpx;\n}\n\n.register-link {\n\tcolor: rgba(255, 255, 255, 0.9);\n\tfont-size: 28rpx;\n\ttext-decoration: underline;\n}\n\n.quick-login-tip {\n\tbackground-color: rgba(255, 255, 255, 0.1);\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tmargin-top: 60rpx;\n\ttext-align: center;\n}\n\n.tip-text {\n\tdisplay: block;\n\tcolor: rgba(255, 255, 255, 0.8);\n\tfont-size: 24rpx;\n\tmargin-bottom: 10rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userLogin.vue?vue&type=style&index=0&id=7264ae84&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./userLogin.vue?vue&type=style&index=0&id=7264ae84&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754093677269\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}