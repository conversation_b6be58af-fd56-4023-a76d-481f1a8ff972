<template>
	<view class="my-container">
		<!-- 用户信息区域 -->
		<view class="user-section">
			<view class="user-info" v-if="isLoggedIn">
				<view class="user-avatar" v-if="!userInfo.avatar">👤</view>
				<image v-else :src="userInfo.avatar" class="user-avatar" mode="aspectFill"></image>
				<view class="user-details">
					<text class="user-nickname">{{ userInfo.nickname || userInfo.username }}</text>
					<text class="user-username">@{{ userInfo.username }}</text>
				</view>
				<view class="user-actions">
					<button class="edit-btn" @click="goToEditProfile">编辑</button>
				</view>
			</view>
			
			<view class="login-prompt" v-else>
				<view class="user-avatar">👤</view>
				<view class="login-info">
					<text class="login-text">点击登录</text>
					<text class="login-tip">登录后可发布和收藏房源</text>
				</view>
				<button class="login-btn" @click="goToLogin">登录</button>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-item" @click="goToMyHouses">
				<text class="menu-icon">🏠</text>
				<text class="menu-text">我的发布</text>
				<text class="menu-count" v-if="myHousesCount > 0">{{ myHousesCount }}</text>
				<text class="menu-arrow">></text>
			</view>
			
			<view class="menu-item" @click="goToMyFavorites">
				<text class="menu-icon">❤️</text>
				<text class="menu-text">我的收藏</text>
				<text class="menu-count" v-if="favoritesCount > 0">{{ favoritesCount }}</text>
				<text class="menu-arrow">></text>
			</view>
			
			<view class="menu-item" @click="goToSettings">
				<text class="menu-icon">⚙️</text>
				<text class="menu-text">设置</text>
				<text class="menu-arrow">></text>
			</view>
		</view>

		<!-- 其他功能 -->
		<view class="other-section">
			<view class="menu-item" @click="contactService">
				<text class="menu-icon">💬</text>
				<text class="menu-text">联系客服</text>
				<text class="menu-arrow">></text>
			</view>
			
			<view class="menu-item" @click="showAbout">
				<text class="menu-icon">ℹ️</text>
				<text class="menu-text">关于我们</text>
				<text class="menu-arrow">></text>
			</view>
		</view>

		<!-- 退出登录 -->
		<view class="logout-section" v-if="isLoggedIn">
			<button class="logout-btn" @click="handleLogout">退出登录</button>
		</view>

		<!-- 我的发布弹窗 -->
		<view class="modal" v-if="showMyHousesModal" @click="closeMyHousesModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">我的发布</text>
					<text class="modal-close" @click="closeMyHousesModal">✕</text>
				</view>
				<scroll-view class="modal-body" scroll-y="true">
					<view class="house-item" v-for="house in myHouses" :key="house._id" @click="goToHouseDetail(house._id)">
						<image :src="house.images[0] || 'https://via.placeholder.com/240x180/CCCCCC/FFFFFF?text=暂无图片'" class="house-image" mode="aspectFill"></image>
						<view class="house-info">
							<text class="house-title">{{ house.title }}</text>
							<text class="house-price">¥{{ house.price }}/月</text>
							<view class="house-status">
								<text class="status-text" :class="'status-' + house.status">
									{{ getStatusText(house.status) }}
								</text>
							</view>
						</view>
					</view>
					<view class="empty-tip" v-if="myHouses.length === 0">
						暂无发布的房源
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 我的收藏弹窗 -->
		<view class="modal" v-if="showFavoritesModal" @click="closeFavoritesModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">我的收藏</text>
					<text class="modal-close" @click="closeFavoritesModal">✕</text>
				</view>
				<scroll-view class="modal-body" scroll-y="true">
					<view class="house-item" v-for="house in favorites" :key="house._id" @click="goToHouseDetail(house._id)">
						<image :src="house.images[0] || 'https://via.placeholder.com/240x180/CCCCCC/FFFFFF?text=暂无图片'" class="house-image" mode="aspectFill"></image>
						<view class="house-info">
							<text class="house-title">{{ house.title }}</text>
							<text class="house-price">¥{{ house.price }}/月</text>
							<text class="house-location">{{ house.location.address }}</text>
						</view>
					</view>
					<view class="empty-tip" v-if="favorites.length === 0">
						暂无收藏的房源
					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script>
import { houseManage, userFavorites, utils } from '@/utils/api.js';

export default {
	data() {
		return {
			userInfo: {},
			isLoggedIn: false,
			myHousesCount: 0,
			favoritesCount: 0,
			showMyHousesModal: false,
			showFavoritesModal: false,
			myHouses: [],
			favorites: []
		}
	},
	
	onShow() {
		this.checkLoginStatus();
		if (this.isLoggedIn) {
			this.loadUserData();
		}
	},
	
	onLoad(options) {
		// 检查是否有特定的tab参数
		if (options.tab === 'favorites') {
			this.goToMyFavorites();
		}
	},
	
	methods: {
		// 检查登录状态
		checkLoginStatus() {
			this.isLoggedIn = utils.isLoggedIn();
			if (this.isLoggedIn) {
				this.userInfo = utils.getUserInfo();
			}
		},
		
		// 加载用户数据
		async loadUserData() {
			await Promise.all([
				this.loadMyHousesCount(),
				this.loadFavoritesCount()
			]);
		},
		
		// 加载我的发布数量
		async loadMyHousesCount() {
			try {
				const res = await houseManage.getMyHouses({ page: 1, pageSize: 1 });
				if (res.code === 200) {
					this.myHousesCount = res.data.total;
				}
			} catch (error) {
				console.error('加载我的发布数量失败:', error);
			}
		},
		
		// 加载收藏数量
		async loadFavoritesCount() {
			try {
				const res = await userFavorites.getFavorites({ page: 1, pageSize: 1 });
				if (res.code === 200) {
					this.favoritesCount = res.data.total;
				}
			} catch (error) {
				console.error('加载收藏数量失败:', error);
			}
		},
		
		// 跳转到登录页面
		goToLogin() {
			utils.goToLogin();
		},
		
		// 跳转到编辑资料页面
		goToEditProfile() {
			uni.showToast({
				title: '编辑资料功能开发中',
				icon: 'none'
			});
		},
		
		// 显示我的发布
		async goToMyHouses() {
			if (!this.isLoggedIn) {
				this.goToLogin();
				return;
			}
			
			try {
				const res = await houseManage.getMyHouses({ page: 1, pageSize: 20 });
				if (res.code === 200) {
					this.myHouses = res.data.list;
					this.showMyHousesModal = true;
				} else {
					utils.showError(res.message);
				}
			} catch (error) {
				console.error('加载我的发布失败:', error);
				utils.showError('加载失败');
			}
		},
		
		// 显示我的收藏
		async goToMyFavorites() {
			if (!this.isLoggedIn) {
				this.goToLogin();
				return;
			}
			
			try {
				const res = await userFavorites.getFavorites({ page: 1, pageSize: 20 });
				if (res.code === 200) {
					this.favorites = res.data.list;
					this.showFavoritesModal = true;
				} else {
					utils.showError(res.message);
				}
			} catch (error) {
				console.error('加载我的收藏失败:', error);
				utils.showError('加载失败');
			}
		},
		
		// 跳转到设置页面
		goToSettings() {
			uni.showToast({
				title: '设置功能开发中',
				icon: 'none'
			});
		},
		
		// 联系客服
		contactService() {
			uni.showModal({
				title: '联系客服',
				content: '客服电话：400-123-4567\n工作时间：9:00-18:00\n\n或添加客服微信：service123',
				showCancel: false
			});
		},
		
		// 显示关于我们
		showAbout() {
			uni.showModal({
				title: '关于我们',
				content: '租房小助手 v1.0.0\n\n一个专业的租房信息平台\n为您提供优质的租房服务',
				showCancel: false
			});
		},
		
		// 退出登录
		handleLogout() {
			uni.showModal({
				title: '确认退出',
				content: '确定要退出登录吗？',
				success: (res) => {
					if (res.confirm) {
						utils.clearUserData();
						this.checkLoginStatus();
						utils.showSuccess('已退出登录');
					}
				}
			});
		},
		
		// 关闭我的发布弹窗
		closeMyHousesModal() {
			this.showMyHousesModal = false;
		},
		
		// 关闭我的收藏弹窗
		closeFavoritesModal() {
			this.showFavoritesModal = false;
		},
		
		// 跳转到房源详情
		goToHouseDetail(houseId) {
			uni.navigateTo({
				url: `/pages/houseDetail/houseDetail?id=${houseId}`
			});
		},
		
		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'pending': '待审核',
				'approved': '已通过',
				'rejected': '已驳回'
			};
			return statusMap[status] || '未知';
		}
	}
}
</script>

<style scoped>
.my-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 用户信息区域 */
.user-section {
	background-color: #fff;
	padding: 40rpx 30rpx;
	margin-bottom: 20rpx;
}

.user-info,
.login-prompt {
	display: flex;
	align-items: center;
}

.user-avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	margin-right: 30rpx;
}

.user-details,
.login-info {
	flex: 1;
}

.user-nickname,
.login-text {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 10rpx;
}

.user-username,
.login-tip {
	font-size: 26rpx;
	color: #999;
	display: block;
}

.edit-btn,
.login-btn {
	background-color: #007AFF;
	color: #fff;
	border: none;
	border-radius: 30rpx;
	padding: 15rpx 30rpx;
	font-size: 26rpx;
}

/* 菜单区域 */
.menu-section,
.other-section {
	background-color: #fff;
	margin-bottom: 20rpx;
}

.menu-item {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
	border-bottom: none;
}

.menu-icon {
	font-size: 40rpx;
	margin-right: 30rpx;
}

.menu-text {
	flex: 1;
	font-size: 30rpx;
	color: #333;
}

.menu-count {
	background-color: #FF3B30;
	color: #fff;
	font-size: 20rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
	margin-right: 20rpx;
}

.menu-arrow {
	font-size: 28rpx;
	color: #999;
}

/* 退出登录 */
.logout-section {
	padding: 30rpx;
}

.logout-btn {
	width: 100%;
	height: 80rpx;
	background-color: #FF3B30;
	color: #fff;
	border: none;
	border-radius: 40rpx;
	font-size: 28rpx;
}

/* 弹窗样式 */
.modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.modal-content {
	background-color: #fff;
	border-radius: 20rpx;
	width: 90%;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
}

.modal-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.modal-close {
	font-size: 40rpx;
	color: #999;
	padding: 10rpx;
}

.modal-body {
	flex: 1;
	padding: 20rpx;
}

.house-item {
	display: flex;
	background-color: #f8f9fa;
	border-radius: 15rpx;
	padding: 20rpx;
	margin-bottom: 20rpx;
}

.house-image {
	width: 120rpx;
	height: 120rpx;
	border-radius: 10rpx;
	margin-right: 20rpx;
}

.house-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

.house-title {
	font-size: 28rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.house-price {
	font-size: 26rpx;
	color: #FF3B30;
	font-weight: bold;
	margin-bottom: 10rpx;
}

.house-location {
	font-size: 24rpx;
	color: #999;
}

.house-status {
	margin-top: 10rpx;
}

.status-text {
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 10rpx;
}

.status-pending {
	background-color: #FFF3E0;
	color: #FF9500;
}

.status-approved {
	background-color: #E8F5E8;
	color: #34C759;
}

.status-rejected {
	background-color: #FCE4EC;
	color: #FF3B30;
}

.empty-tip {
	text-align: center;
	color: #999;
	font-size: 28rpx;
	padding: 60rpx 20rpx;
}
</style>
