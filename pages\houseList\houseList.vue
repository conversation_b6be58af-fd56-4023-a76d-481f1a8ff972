<template>
	<view class="house-list-container">
		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-bar">
				<input 
					class="search-input" 
					type="text" 
					placeholder="搜索房源、位置、关键词" 
					v-model="searchKeyword"
					@confirm="handleSearch"
					confirm-type="search"
				/>
				<button class="search-btn" @click="handleSearch">搜索</button>
			</view>
		</view>

		<!-- 筛选栏 -->
		<view class="filter-section">
			<scroll-view class="filter-scroll" scroll-x="true">
				<view class="filter-item" :class="{ active: !filterData.type }" @click="selectType('')">
					全部
				</view>
				<view 
					class="filter-item" 
					:class="{ active: filterData.type === type }" 
					v-for="type in houseTypes" 
					:key="type"
					@click="selectType(type)"
				>
					{{ type }}
				</view>
			</scroll-view>
			<view class="filter-more" @click="showFilterModal = true">
				筛选
			</view>
		</view>

		<!-- 房源列表 -->
		<view class="house-list">
			<view 
				class="house-item" 
				v-for="(house, index) in houseList" 
				:key="house._id"
				@click="goToHouseDetail(house._id)"
			>
				<image :src="house.images[0] || 'https://via.placeholder.com/400x300/CCCCCC/FFFFFF?text=暂无图片'" class="house-image" mode="aspectFill"></image>
				<view class="house-info">
					<text class="house-title">{{ house.title }}</text>
					<text class="house-desc">{{ house.desc }}</text>
					<view class="house-tags">
						<text class="house-tag">{{ house.type }}</text>
						<text class="house-tag" v-for="config in house.config.slice(0, 3)" :key="config">{{ config }}</text>
					</view>
					<view class="house-location">
						<text class="location-icon">📍</text>
						<text class="location-text">{{ house.location.address }}</text>
					</view>
					<view class="house-bottom">
						<text class="house-price">¥{{ house.price }}/月</text>
						<text class="house-time">{{ formatTime(house.created_at) }}</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-section" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 空状态 -->
		<view class="empty-section" v-if="!loading && houseList.length === 0">
			<text class="empty-icon">🏠</text>
			<text class="empty-text">暂无房源信息</text>
			<text class="empty-tip">试试调整筛选条件</text>
		</view>

		<!-- 筛选弹窗 -->
		<view class="filter-modal" v-if="showFilterModal" @click="closeFilterModal">
			<view class="filter-content" @click.stop>
				<view class="filter-header">
					<text class="filter-title">筛选条件</text>
					<text class="filter-close" @click="closeFilterModal">✕</text>
				</view>

				<view class="filter-group">
					<text class="filter-group-title">价格范围</text>
					<view class="price-range">
						<input 
							class="price-input" 
							type="number" 
							placeholder="最低价" 
							v-model="tempFilter.priceMin"
						/>
						<text class="price-separator">-</text>
						<input 
							class="price-input" 
							type="number" 
							placeholder="最高价" 
							v-model="tempFilter.priceMax"
						/>
					</view>
				</view>

				<view class="filter-group">
					<text class="filter-group-title">地区</text>
					<input 
						class="location-input" 
						type="text" 
						placeholder="输入地区关键词" 
						v-model="tempFilter.location"
					/>
				</view>

				<view class="filter-actions">
					<button class="filter-reset" @click="resetFilter">重置</button>
					<button class="filter-confirm" @click="applyFilter">确定</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			searchKeyword: '',
			houseList: [],
			loading: false,
			currentPage: 1,
			pageSize: 10,
			hasMore: true,
			showFilterModal: false,
			
			// 房型选项
			houseTypes: ['一居室', '二居室', '三居室', '四居室', '合租', '单间'],
			
			// 筛选条件
			filterData: {
				type: '',
				priceMin: '',
				priceMax: '',
				location: ''
			},
			
			// 临时筛选条件（弹窗中的）
			tempFilter: {
				priceMin: '',
				priceMax: '',
				location: ''
			}
		}
	},
	
	onLoad(options) {
		// 如果是从搜索进入，显示搜索状态
		if (options.search) {
			// 可以在这里设置搜索焦点等
		}
		this.loadHouseList();
	},
	
	onPullDownRefresh() {
		this.refreshHouseList();
	},
	
	onReachBottom() {
		if (this.hasMore && !this.loading) {
			this.loadMoreHouses();
		}
	},
	
	methods: {
		// 加载房源列表
		async loadHouseList(isRefresh = false) {
			if (this.loading) return;
			
			this.loading = true;
			
			if (isRefresh) {
				this.currentPage = 1;
				this.hasMore = true;
			}
			
			try {
				const requestData = {
					page: this.currentPage,
					pageSize: this.pageSize
				};
				
				// 添加筛选条件
				if (this.filterData.type) {
					requestData.type = this.filterData.type;
				}
				if (this.filterData.priceMin) {
					requestData.priceMin = parseInt(this.filterData.priceMin);
				}
				if (this.filterData.priceMax) {
					requestData.priceMax = parseInt(this.filterData.priceMax);
				}
				if (this.filterData.location) {
					requestData.location = this.filterData.location;
				}
				
				let res;
				
				// 如果有搜索关键词，使用搜索接口
				if (this.searchKeyword.trim()) {
					res = await uniCloud.callFunction({
						name: 'house-manage',
						data: {
							action: 'searchHouses',
							data: {
								keyword: this.searchKeyword.trim(),
								page: this.currentPage,
								pageSize: this.pageSize
							}
						}
					});
				} else {
					res = await uniCloud.callFunction({
						name: 'house-manage',
						data: {
							action: 'getHouseList',
							data: requestData
						}
					});
				}
				
				if (res.result.code === 200) {
					const newList = res.result.data.list || [];
					
					if (isRefresh || this.currentPage === 1) {
						this.houseList = newList;
					} else {
						this.houseList = [...this.houseList, ...newList];
					}
					
					// 判断是否还有更多数据
					this.hasMore = newList.length === this.pageSize;
					
				} else {
					uni.showToast({
						title: res.result.message || '加载失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('加载房源列表失败:', error);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			} finally {
				this.loading = false;
				if (isRefresh) {
					uni.stopPullDownRefresh();
				}
			}
		},
		
		// 刷新房源列表
		refreshHouseList() {
			this.loadHouseList(true);
		},
		
		// 加载更多房源
		loadMoreHouses() {
			this.currentPage++;
			this.loadHouseList();
		},
		
		// 处理搜索
		handleSearch() {
			this.refreshHouseList();
		},
		
		// 选择房型
		selectType(type) {
			this.filterData.type = type;
			this.refreshHouseList();
		},
		
		// 关闭筛选弹窗
		closeFilterModal() {
			this.showFilterModal = false;
			// 重置临时筛选条件
			this.tempFilter = {
				priceMin: this.filterData.priceMin,
				priceMax: this.filterData.priceMax,
				location: this.filterData.location
			};
		},
		
		// 重置筛选条件
		resetFilter() {
			this.tempFilter = {
				priceMin: '',
				priceMax: '',
				location: ''
			};
		},
		
		// 应用筛选条件
		applyFilter() {
			this.filterData.priceMin = this.tempFilter.priceMin;
			this.filterData.priceMax = this.tempFilter.priceMax;
			this.filterData.location = this.tempFilter.location;
			this.showFilterModal = false;
			this.refreshHouseList();
		},
		
		// 跳转到房源详情
		goToHouseDetail(houseId) {
			uni.navigateTo({
				url: `/pages/houseDetail/houseDetail?id=${houseId}`
			});
		},
		
		// 格式化时间
		formatTime(timestamp) {
			const date = new Date(timestamp);
			const now = new Date();
			const diff = now - date;
			
			if (diff < 24 * 60 * 60 * 1000) {
				return '今天';
			} else if (diff < 2 * 24 * 60 * 60 * 1000) {
				return '昨天';
			} else {
				return `${date.getMonth() + 1}-${date.getDate()}`;
			}
		}
	}
}
</script>

<style scoped>
.house-list-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 搜索栏样式 */
.search-section {
	background-color: #fff;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #eee;
}

.search-bar {
	display: flex;
	align-items: center;
	background-color: #f8f9fa;
	border-radius: 50rpx;
	padding: 0 30rpx;
}

.search-input {
	flex: 1;
	height: 80rpx;
	font-size: 28rpx;
	color: #333;
}

.search-btn {
	background-color: #007AFF;
	color: #fff;
	border: none;
	border-radius: 40rpx;
	padding: 15rpx 30rpx;
	font-size: 26rpx;
	margin-left: 20rpx;
}

/* 筛选栏样式 */
.filter-section {
	background-color: #fff;
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #eee;
}

.filter-scroll {
	flex: 1;
	white-space: nowrap;
}

.filter-item {
	display: inline-block;
	padding: 15rpx 30rpx;
	margin: 0 15rpx;
	background-color: #f8f9fa;
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #666;
}

.filter-item.active {
	background-color: #007AFF;
	color: #fff;
}

.filter-more {
	padding: 15rpx 30rpx;
	margin-right: 30rpx;
	background-color: #f8f9fa;
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #666;
}

/* 房源列表样式 */
.house-list {
	padding: 20rpx 30rpx;
}

.house-item {
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	display: flex;
}

.house-image {
	width: 200rpx;
	height: 200rpx;
	flex-shrink: 0;
}

.house-info {
	flex: 1;
	padding: 20rpx;
	display: flex;
	flex-direction: column;
}

.house-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.house-desc {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 15rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.house-tags {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 15rpx;
}

.house-tag {
	background-color: #f0f0f0;
	color: #666;
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
	margin-right: 10rpx;
	margin-bottom: 5rpx;
}

.house-location {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}

.location-icon {
	font-size: 24rpx;
	margin-right: 10rpx;
}

.location-text {
	font-size: 24rpx;
	color: #999;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.house-bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: auto;
}

.house-price {
	font-size: 32rpx;
	font-weight: bold;
	color: #FF3B30;
}

.house-time {
	font-size: 22rpx;
	color: #999;
}

/* 加载状态样式 */
.loading-section {
	padding: 40rpx;
	text-align: center;
}

.loading-text {
	color: #999;
	font-size: 28rpx;
}

/* 空状态样式 */
.empty-section {
	padding: 100rpx 40rpx;
	text-align: center;
}

.empty-icon {
	font-size: 120rpx;
	display: block;
	margin-bottom: 30rpx;
}

.empty-text {
	font-size: 32rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}

.empty-tip {
	font-size: 26rpx;
	color: #999;
	display: block;
}

/* 筛选弹窗样式 */
.filter-modal {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

.filter-content {
	background-color: #fff;
	border-radius: 30rpx 30rpx 0 0;
	padding: 40rpx 30rpx;
	width: 100%;
	max-height: 80vh;
}

.filter-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.filter-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.filter-close {
	font-size: 40rpx;
	color: #999;
	padding: 10rpx;
}

.filter-group {
	margin-bottom: 40rpx;
}

.filter-group-title {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}

.price-range {
	display: flex;
	align-items: center;
}

.price-input {
	flex: 1;
	height: 80rpx;
	border: 1rpx solid #ddd;
	border-radius: 10rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
}

.price-separator {
	margin: 0 20rpx;
	color: #666;
}

.location-input {
	width: 100%;
	height: 80rpx;
	border: 1rpx solid #ddd;
	border-radius: 10rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
}

.filter-actions {
	display: flex;
	gap: 20rpx;
	margin-top: 60rpx;
}

.filter-reset {
	flex: 1;
	height: 80rpx;
	background-color: #f8f9fa;
	color: #666;
	border: none;
	border-radius: 10rpx;
	font-size: 28rpx;
}

.filter-confirm {
	flex: 1;
	height: 80rpx;
	background-color: #007AFF;
	color: #fff;
	border: none;
	border-radius: 10rpx;
	font-size: 28rpx;
}
</style>
