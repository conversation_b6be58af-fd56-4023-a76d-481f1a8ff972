{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端11/pages/houseList/houseList.vue?ce4a", "webpack:///D:/web/project/前端11/pages/houseList/houseList.vue?d836", "webpack:///D:/web/project/前端11/pages/houseList/houseList.vue?9be6", "webpack:///D:/web/project/前端11/pages/houseList/houseList.vue?4b69", "uni-app:///pages/houseList/houseList.vue", "webpack:///D:/web/project/前端11/pages/houseList/houseList.vue?907d", "webpack:///D:/web/project/前端11/pages/houseList/houseList.vue?f772"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "searchKeyword", "houseList", "loading", "currentPage", "pageSize", "hasMore", "showFilterModal", "houseTypes", "filterData", "type", "priceMin", "priceMax", "location", "temp<PERSON><PERSON>er", "onLoad", "onPullDownRefresh", "onReachBottom", "methods", "loadHouseList", "isRefresh", "requestData", "page", "uniCloud", "name", "action", "keyword", "res", "newList", "uni", "title", "icon", "console", "refreshHouseList", "loadMoreHouses", "handleSearch", "selectType", "closeFilterModal", "resetFilter", "applyFilter", "goToHouseDetail", "url", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACqC;;;AAG7F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAooB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6HxpB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MAEA;MACAC;QACAH;QACAC;QACAC;MACA;IACA;EACA;EAEAE;IACA;IACA;MACA;IAAA;IAEA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAEA;gBAEA;kBACA;kBACA;gBACA;gBAAA;gBAGAC;kBACAC;kBACAjB;gBACA,GAEA;gBACA;kBACAgB;gBACA;gBACA;kBACAA;gBACA;gBACA;kBACAA;gBACA;gBACA;kBACAA;gBACA;gBAAA,KAKA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAE;kBACAC;kBACAxB;oBACAyB;oBACAzB;sBACA0B;sBACAJ;sBACAjB;oBACA;kBACA;gBACA;cAAA;gBAVAsB;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAYAJ;kBACAC;kBACAxB;oBACAyB;oBACAzB;kBACA;gBACA;cAAA;gBANA2B;cAAA;gBASA;kBACAC;kBAEA;oBACA;kBACA;oBACA;kBACA;;kBAEA;kBACA;gBAEA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAH;kBACAC;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBACA;kBACAF;gBACA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAI;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA1B;QACAC;QACAC;MACA;IACA;IAEA;IACAyB;MACA;QACA3B;QACAC;QACAC;MACA;IACA;IAEA;IACA0B;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACAX;QACAY;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChVA;AAAA;AAAA;AAAA;AAA27B,CAAgB,q5BAAG,EAAC,C;;;;;;;;;;;ACA/8B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/houseList/houseList.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/houseList/houseList.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./houseList.vue?vue&type=template&id=5fd6dcf8&scoped=true&\"\nvar renderjs\nimport script from \"./houseList.vue?vue&type=script&lang=js&\"\nexport * from \"./houseList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./houseList.vue?vue&type=style&index=0&id=5fd6dcf8&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5fd6dcf8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/houseList/houseList.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseList.vue?vue&type=template&id=5fd6dcf8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.__map(_vm.houseList, function (house, index) {\n    var $orig = _vm.__get_orig(house)\n    var l0 = house.config.slice(0, 3)\n    var m0 = _vm.formatTime(house.created_at)\n    return {\n      $orig: $orig,\n      l0: l0,\n      m0: m0,\n    }\n  })\n  var g0 = !_vm.loading && _vm.houseList.length === 0\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showFilterModal = true\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseList.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"house-list-container\">\n\t\t<!-- 搜索栏 -->\n\t\t<view class=\"search-section\">\n\t\t\t<view class=\"search-bar\">\n\t\t\t\t<input \n\t\t\t\t\tclass=\"search-input\" \n\t\t\t\t\ttype=\"text\" \n\t\t\t\t\tplaceholder=\"搜索房源、位置、关键词\" \n\t\t\t\t\tv-model=\"searchKeyword\"\n\t\t\t\t\t@confirm=\"handleSearch\"\n\t\t\t\t\tconfirm-type=\"search\"\n\t\t\t\t/>\n\t\t\t\t<button class=\"search-btn\" @click=\"handleSearch\">搜索</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 筛选栏 -->\n\t\t<view class=\"filter-section\">\n\t\t\t<scroll-view class=\"filter-scroll\" scroll-x=\"true\">\n\t\t\t\t<view class=\"filter-item\" :class=\"{ active: !filterData.type }\" @click=\"selectType('')\">\n\t\t\t\t\t全部\n\t\t\t\t</view>\n\t\t\t\t<view \n\t\t\t\t\tclass=\"filter-item\" \n\t\t\t\t\t:class=\"{ active: filterData.type === type }\" \n\t\t\t\t\tv-for=\"type in houseTypes\" \n\t\t\t\t\t:key=\"type\"\n\t\t\t\t\t@click=\"selectType(type)\"\n\t\t\t\t>\n\t\t\t\t\t{{ type }}\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t\t<view class=\"filter-more\" @click=\"showFilterModal = true\">\n\t\t\t\t筛选\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 房源列表 -->\n\t\t<view class=\"house-list\">\n\t\t\t<view \n\t\t\t\tclass=\"house-item\" \n\t\t\t\tv-for=\"(house, index) in houseList\" \n\t\t\t\t:key=\"house._id\"\n\t\t\t\t@click=\"goToHouseDetail(house._id)\"\n\t\t\t>\n\t\t\t\t<image :src=\"house.images[0] || 'https://via.placeholder.com/400x300/CCCCCC/FFFFFF?text=暂无图片'\" class=\"house-image\" mode=\"aspectFill\"></image>\n\t\t\t\t<view class=\"house-info\">\n\t\t\t\t\t<text class=\"house-title\">{{ house.title }}</text>\n\t\t\t\t\t<text class=\"house-desc\">{{ house.desc }}</text>\n\t\t\t\t\t<view class=\"house-tags\">\n\t\t\t\t\t\t<text class=\"house-tag\">{{ house.type }}</text>\n\t\t\t\t\t\t<text class=\"house-tag\" v-for=\"config in house.config.slice(0, 3)\" :key=\"config\">{{ config }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"house-location\">\n\t\t\t\t\t\t<text class=\"location-icon\">📍</text>\n\t\t\t\t\t\t<text class=\"location-text\">{{ house.location.address }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"house-bottom\">\n\t\t\t\t\t\t<text class=\"house-price\">¥{{ house.price }}/月</text>\n\t\t\t\t\t\t<text class=\"house-time\">{{ formatTime(house.created_at) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 加载状态 -->\n\t\t<view class=\"loading-section\" v-if=\"loading\">\n\t\t\t<text class=\"loading-text\">加载中...</text>\n\t\t</view>\n\n\t\t<!-- 空状态 -->\n\t\t<view class=\"empty-section\" v-if=\"!loading && houseList.length === 0\">\n\t\t\t<text class=\"empty-icon\">🏠</text>\n\t\t\t<text class=\"empty-text\">暂无房源信息</text>\n\t\t\t<text class=\"empty-tip\">试试调整筛选条件</text>\n\t\t</view>\n\n\t\t<!-- 筛选弹窗 -->\n\t\t<view class=\"filter-modal\" v-if=\"showFilterModal\" @click=\"closeFilterModal\">\n\t\t\t<view class=\"filter-content\" @click.stop>\n\t\t\t\t<view class=\"filter-header\">\n\t\t\t\t\t<text class=\"filter-title\">筛选条件</text>\n\t\t\t\t\t<text class=\"filter-close\" @click=\"closeFilterModal\">✕</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"filter-group\">\n\t\t\t\t\t<text class=\"filter-group-title\">价格范围</text>\n\t\t\t\t\t<view class=\"price-range\">\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"price-input\" \n\t\t\t\t\t\t\ttype=\"number\" \n\t\t\t\t\t\t\tplaceholder=\"最低价\" \n\t\t\t\t\t\t\tv-model=\"tempFilter.priceMin\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t\t<text class=\"price-separator\">-</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"price-input\" \n\t\t\t\t\t\t\ttype=\"number\" \n\t\t\t\t\t\t\tplaceholder=\"最高价\" \n\t\t\t\t\t\t\tv-model=\"tempFilter.priceMax\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"filter-group\">\n\t\t\t\t\t<text class=\"filter-group-title\">地区</text>\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"location-input\" \n\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\tplaceholder=\"输入地区关键词\" \n\t\t\t\t\t\tv-model=\"tempFilter.location\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"filter-actions\">\n\t\t\t\t\t<button class=\"filter-reset\" @click=\"resetFilter\">重置</button>\n\t\t\t\t\t<button class=\"filter-confirm\" @click=\"applyFilter\">确定</button>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tsearchKeyword: '',\n\t\t\thouseList: [],\n\t\t\tloading: false,\n\t\t\tcurrentPage: 1,\n\t\t\tpageSize: 10,\n\t\t\thasMore: true,\n\t\t\tshowFilterModal: false,\n\t\t\t\n\t\t\t// 房型选项\n\t\t\thouseTypes: ['一居室', '二居室', '三居室', '四居室', '合租', '单间'],\n\t\t\t\n\t\t\t// 筛选条件\n\t\t\tfilterData: {\n\t\t\t\ttype: '',\n\t\t\t\tpriceMin: '',\n\t\t\t\tpriceMax: '',\n\t\t\t\tlocation: ''\n\t\t\t},\n\t\t\t\n\t\t\t// 临时筛选条件（弹窗中的）\n\t\t\ttempFilter: {\n\t\t\t\tpriceMin: '',\n\t\t\t\tpriceMax: '',\n\t\t\t\tlocation: ''\n\t\t\t}\n\t\t}\n\t},\n\t\n\tonLoad(options) {\n\t\t// 如果是从搜索进入，显示搜索状态\n\t\tif (options.search) {\n\t\t\t// 可以在这里设置搜索焦点等\n\t\t}\n\t\tthis.loadHouseList();\n\t},\n\t\n\tonPullDownRefresh() {\n\t\tthis.refreshHouseList();\n\t},\n\t\n\tonReachBottom() {\n\t\tif (this.hasMore && !this.loading) {\n\t\t\tthis.loadMoreHouses();\n\t\t}\n\t},\n\t\n\tmethods: {\n\t\t// 加载房源列表\n\t\tasync loadHouseList(isRefresh = false) {\n\t\t\tif (this.loading) return;\n\t\t\t\n\t\t\tthis.loading = true;\n\t\t\t\n\t\t\tif (isRefresh) {\n\t\t\t\tthis.currentPage = 1;\n\t\t\t\tthis.hasMore = true;\n\t\t\t}\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst requestData = {\n\t\t\t\t\tpage: this.currentPage,\n\t\t\t\t\tpageSize: this.pageSize\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// 添加筛选条件\n\t\t\t\tif (this.filterData.type) {\n\t\t\t\t\trequestData.type = this.filterData.type;\n\t\t\t\t}\n\t\t\t\tif (this.filterData.priceMin) {\n\t\t\t\t\trequestData.priceMin = parseInt(this.filterData.priceMin);\n\t\t\t\t}\n\t\t\t\tif (this.filterData.priceMax) {\n\t\t\t\t\trequestData.priceMax = parseInt(this.filterData.priceMax);\n\t\t\t\t}\n\t\t\t\tif (this.filterData.location) {\n\t\t\t\t\trequestData.location = this.filterData.location;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tlet res;\n\t\t\t\t\n\t\t\t\t// 如果有搜索关键词，使用搜索接口\n\t\t\t\tif (this.searchKeyword.trim()) {\n\t\t\t\t\tres = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'house-manage',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'searchHouses',\n\t\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\t\tkeyword: this.searchKeyword.trim(),\n\t\t\t\t\t\t\t\tpage: this.currentPage,\n\t\t\t\t\t\t\t\tpageSize: this.pageSize\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tres = await uniCloud.callFunction({\n\t\t\t\t\t\tname: 'house-manage',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\taction: 'getHouseList',\n\t\t\t\t\t\t\tdata: requestData\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\tif (res.result.code === 200) {\n\t\t\t\t\tconst newList = res.result.data.list || [];\n\t\t\t\t\t\n\t\t\t\t\tif (isRefresh || this.currentPage === 1) {\n\t\t\t\t\t\tthis.houseList = newList;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.houseList = [...this.houseList, ...newList];\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// 判断是否还有更多数据\n\t\t\t\t\tthis.hasMore = newList.length === this.pageSize;\n\t\t\t\t\t\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: res.result.message || '加载失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('加载房源列表失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '网络错误',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t\tif (isRefresh) {\n\t\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 刷新房源列表\n\t\trefreshHouseList() {\n\t\t\tthis.loadHouseList(true);\n\t\t},\n\t\t\n\t\t// 加载更多房源\n\t\tloadMoreHouses() {\n\t\t\tthis.currentPage++;\n\t\t\tthis.loadHouseList();\n\t\t},\n\t\t\n\t\t// 处理搜索\n\t\thandleSearch() {\n\t\t\tthis.refreshHouseList();\n\t\t},\n\t\t\n\t\t// 选择房型\n\t\tselectType(type) {\n\t\t\tthis.filterData.type = type;\n\t\t\tthis.refreshHouseList();\n\t\t},\n\t\t\n\t\t// 关闭筛选弹窗\n\t\tcloseFilterModal() {\n\t\t\tthis.showFilterModal = false;\n\t\t\t// 重置临时筛选条件\n\t\t\tthis.tempFilter = {\n\t\t\t\tpriceMin: this.filterData.priceMin,\n\t\t\t\tpriceMax: this.filterData.priceMax,\n\t\t\t\tlocation: this.filterData.location\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 重置筛选条件\n\t\tresetFilter() {\n\t\t\tthis.tempFilter = {\n\t\t\t\tpriceMin: '',\n\t\t\t\tpriceMax: '',\n\t\t\t\tlocation: ''\n\t\t\t};\n\t\t},\n\t\t\n\t\t// 应用筛选条件\n\t\tapplyFilter() {\n\t\t\tthis.filterData.priceMin = this.tempFilter.priceMin;\n\t\t\tthis.filterData.priceMax = this.tempFilter.priceMax;\n\t\t\tthis.filterData.location = this.tempFilter.location;\n\t\t\tthis.showFilterModal = false;\n\t\t\tthis.refreshHouseList();\n\t\t},\n\t\t\n\t\t// 跳转到房源详情\n\t\tgoToHouseDetail(houseId) {\n\t\t\tuni.navigateTo({\n\t\t\t\turl: `/pages/houseDetail/houseDetail?id=${houseId}`\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 格式化时间\n\t\tformatTime(timestamp) {\n\t\t\tconst date = new Date(timestamp);\n\t\t\tconst now = new Date();\n\t\t\tconst diff = now - date;\n\t\t\t\n\t\t\tif (diff < 24 * 60 * 60 * 1000) {\n\t\t\t\treturn '今天';\n\t\t\t} else if (diff < 2 * 24 * 60 * 60 * 1000) {\n\t\t\t\treturn '昨天';\n\t\t\t} else {\n\t\t\t\treturn `${date.getMonth() + 1}-${date.getDate()}`;\n\t\t\t}\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.house-list-container {\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n}\n\n/* 搜索栏样式 */\n.search-section {\n\tbackground-color: #fff;\n\tpadding: 20rpx 30rpx;\n\tborder-bottom: 1rpx solid #eee;\n}\n\n.search-bar {\n\tdisplay: flex;\n\talign-items: center;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 50rpx;\n\tpadding: 0 30rpx;\n}\n\n.search-input {\n\tflex: 1;\n\theight: 80rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.search-btn {\n\tbackground-color: #007AFF;\n\tcolor: #fff;\n\tborder: none;\n\tborder-radius: 40rpx;\n\tpadding: 15rpx 30rpx;\n\tfont-size: 26rpx;\n\tmargin-left: 20rpx;\n}\n\n/* 筛选栏样式 */\n.filter-section {\n\tbackground-color: #fff;\n\tdisplay: flex;\n\talign-items: center;\n\tpadding: 20rpx 0;\n\tborder-bottom: 1rpx solid #eee;\n}\n\n.filter-scroll {\n\tflex: 1;\n\twhite-space: nowrap;\n}\n\n.filter-item {\n\tdisplay: inline-block;\n\tpadding: 15rpx 30rpx;\n\tmargin: 0 15rpx;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 30rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n.filter-item.active {\n\tbackground-color: #007AFF;\n\tcolor: #fff;\n}\n\n.filter-more {\n\tpadding: 15rpx 30rpx;\n\tmargin-right: 30rpx;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 30rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n/* 房源列表样式 */\n.house-list {\n\tpadding: 20rpx 30rpx;\n}\n\n.house-item {\n\tbackground-color: #fff;\n\tborder-radius: 20rpx;\n\tmargin-bottom: 30rpx;\n\toverflow: hidden;\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\n\tdisplay: flex;\n}\n\n.house-image {\n\twidth: 200rpx;\n\theight: 200rpx;\n\tflex-shrink: 0;\n}\n\n.house-info {\n\tflex: 1;\n\tpadding: 20rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n}\n\n.house-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 10rpx;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n\n.house-desc {\n\tfont-size: 26rpx;\n\tcolor: #666;\n\tmargin-bottom: 15rpx;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n\n.house-tags {\n\tdisplay: flex;\n\tflex-wrap: wrap;\n\tmargin-bottom: 15rpx;\n}\n\n.house-tag {\n\tbackground-color: #f0f0f0;\n\tcolor: #666;\n\tfont-size: 22rpx;\n\tpadding: 5rpx 15rpx;\n\tborder-radius: 15rpx;\n\tmargin-right: 10rpx;\n\tmargin-bottom: 5rpx;\n}\n\n.house-location {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 15rpx;\n}\n\n.location-icon {\n\tfont-size: 24rpx;\n\tmargin-right: 10rpx;\n}\n\n.location-text {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\toverflow: hidden;\n\ttext-overflow: ellipsis;\n\twhite-space: nowrap;\n}\n\n.house-bottom {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-top: auto;\n}\n\n.house-price {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #FF3B30;\n}\n\n.house-time {\n\tfont-size: 22rpx;\n\tcolor: #999;\n}\n\n/* 加载状态样式 */\n.loading-section {\n\tpadding: 40rpx;\n\ttext-align: center;\n}\n\n.loading-text {\n\tcolor: #999;\n\tfont-size: 28rpx;\n}\n\n/* 空状态样式 */\n.empty-section {\n\tpadding: 100rpx 40rpx;\n\ttext-align: center;\n}\n\n.empty-icon {\n\tfont-size: 120rpx;\n\tdisplay: block;\n\tmargin-bottom: 30rpx;\n}\n\n.empty-text {\n\tfont-size: 32rpx;\n\tcolor: #666;\n\tdisplay: block;\n\tmargin-bottom: 15rpx;\n}\n\n.empty-tip {\n\tfont-size: 26rpx;\n\tcolor: #999;\n\tdisplay: block;\n}\n\n/* 筛选弹窗样式 */\n.filter-modal {\n\tposition: fixed;\n\ttop: 0;\n\tleft: 0;\n\tright: 0;\n\tbottom: 0;\n\tbackground-color: rgba(0, 0, 0, 0.5);\n\tz-index: 1000;\n\tdisplay: flex;\n\talign-items: flex-end;\n}\n\n.filter-content {\n\tbackground-color: #fff;\n\tborder-radius: 30rpx 30rpx 0 0;\n\tpadding: 40rpx 30rpx;\n\twidth: 100%;\n\tmax-height: 80vh;\n}\n\n.filter-header {\n\tdisplay: flex;\n\tjustify-content: space-between;\n\talign-items: center;\n\tmargin-bottom: 40rpx;\n}\n\n.filter-title {\n\tfont-size: 36rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n}\n\n.filter-close {\n\tfont-size: 40rpx;\n\tcolor: #999;\n\tpadding: 10rpx;\n}\n\n.filter-group {\n\tmargin-bottom: 40rpx;\n}\n\n.filter-group-title {\n\tfont-size: 28rpx;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n\tdisplay: block;\n}\n\n.price-range {\n\tdisplay: flex;\n\talign-items: center;\n}\n\n.price-input {\n\tflex: 1;\n\theight: 80rpx;\n\tborder: 1rpx solid #ddd;\n\tborder-radius: 10rpx;\n\tpadding: 0 20rpx;\n\tfont-size: 28rpx;\n}\n\n.price-separator {\n\tmargin: 0 20rpx;\n\tcolor: #666;\n}\n\n.location-input {\n\twidth: 100%;\n\theight: 80rpx;\n\tborder: 1rpx solid #ddd;\n\tborder-radius: 10rpx;\n\tpadding: 0 20rpx;\n\tfont-size: 28rpx;\n}\n\n.filter-actions {\n\tdisplay: flex;\n\tgap: 20rpx;\n\tmargin-top: 60rpx;\n}\n\n.filter-reset {\n\tflex: 1;\n\theight: 80rpx;\n\tbackground-color: #f8f9fa;\n\tcolor: #666;\n\tborder: none;\n\tborder-radius: 10rpx;\n\tfont-size: 28rpx;\n}\n\n.filter-confirm {\n\tflex: 1;\n\theight: 80rpx;\n\tbackground-color: #007AFF;\n\tcolor: #fff;\n\tborder: none;\n\tborder-radius: 10rpx;\n\tfont-size: 28rpx;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseList.vue?vue&type=style&index=0&id=5fd6dcf8&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./houseList.vue?vue&type=style&index=0&id=5fd6dcf8&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754093677271\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}