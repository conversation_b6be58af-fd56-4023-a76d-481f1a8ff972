/**
 * API 工具类 - 统一管理云函数调用
 */

// 获取用户token
function getUserToken() {
	return uni.getStorageSync('userToken') || '';
}

// 统一的云函数调用方法
async function callCloudFunction(name, action, data = {}) {
	try {
		const res = await uniCloud.callFunction({
			name,
			data: {
				action,
				data
			}
		});
		return res.result;
	} catch (error) {
		console.error(`云函数调用失败 [${name}/${action}]:`, error);
		throw error;
	}
}

// 用户认证相关API
export const userAuth = {
	// 用户注册
	async register(userData) {
		return await callCloudFunction('user-auth', 'register', userData);
	},

	// 用户登录
	async login(loginData) {
		return await callCloudFunction('user-auth', 'login', loginData);
	},

	// 获取用户信息
	async getUserInfo() {
		const token = getUserToken();
		return await callCloudFunction('user-auth', 'getUserInfo', { token });
	},

	// 更新用户资料
	async updateProfile(profileData) {
		const token = getUserToken();
		return await callCloudFunction('user-auth', 'updateProfile', {
			token,
			...profileData
		});
	},

	// 修改密码
	async changePassword(passwordData) {
		const token = getUserToken();
		return await callCloudFunction('user-auth', 'changePassword', {
			token,
			...passwordData
		});
	}
};

// 房源管理相关API
export const houseManage = {
	// 获取房源列表
	async getHouseList(params = {}) {
		return await callCloudFunction('house-manage', 'getHouseList', params);
	},

	// 获取房源详情
	async getHouseDetail(houseId) {
		return await callCloudFunction('house-manage', 'getHouseDetail', { houseId });
	},

	// 发布房源
	async publishHouse(houseData) {
		const token = getUserToken();
		return await callCloudFunction('house-manage', 'publishHouse', {
			token,
			houseData
		});
	},

	// 更新房源
	async updateHouse(houseId, houseData) {
		const token = getUserToken();
		return await callCloudFunction('house-manage', 'updateHouse', {
			token,
			houseId,
			houseData
		});
	},

	// 删除房源
	async deleteHouse(houseId) {
		const token = getUserToken();
		return await callCloudFunction('house-manage', 'deleteHouse', {
			token,
			houseId
		});
	},

	// 获取我的房源
	async getMyHouses(params = {}) {
		const token = getUserToken();
		return await callCloudFunction('house-manage', 'getMyHouses', {
			token,
			...params
		});
	},

	// 搜索房源
	async searchHouses(keyword, params = {}) {
		return await callCloudFunction('house-manage', 'searchHouses', {
			keyword,
			...params
		});
	}
};

// 用户收藏相关API
export const userFavorites = {
	// 添加收藏
	async addFavorite(houseId) {
		const token = getUserToken();
		return await callCloudFunction('user-favorites', 'addFavorite', {
			token,
			houseId
		});
	},

	// 取消收藏
	async removeFavorite(houseId) {
		const token = getUserToken();
		return await callCloudFunction('user-favorites', 'removeFavorite', {
			token,
			houseId
		});
	},

	// 获取收藏列表
	async getFavorites(params = {}) {
		const token = getUserToken();
		return await callCloudFunction('user-favorites', 'getFavorites', {
			token,
			...params
		});
	},

	// 检查收藏状态
	async checkFavorite(houseId) {
		const token = getUserToken();
		return await callCloudFunction('user-favorites', 'checkFavorite', {
			token,
			houseId
		});
	}
};

// 管理员相关API
export const adminManage = {
	// 管理员登录
	async adminLogin(loginData) {
		return await callCloudFunction('admin-manage', 'adminLogin', loginData);
	},

	// 获取房源列表（管理员）
	async getHouseList(params = {}) {
		const token = uni.getStorageSync('adminToken') || '';
		return await callCloudFunction('admin-manage', 'getHouseList', {
			token,
			...params
		});
	},

	// 审核房源
	async auditHouse(houseId, status, reason = '') {
		const token = uni.getStorageSync('adminToken') || '';
		return await callCloudFunction('admin-manage', 'auditHouse', {
			token,
			houseId,
			status,
			reason
		});
	},

	// 删除房源（管理员）
	async deleteHouse(houseId) {
		const token = uni.getStorageSync('adminToken') || '';
		return await callCloudFunction('admin-manage', 'deleteHouse', {
			token,
			houseId
		});
	},

	// 获取用户列表
	async getUserList(params = {}) {
		const token = uni.getStorageSync('adminToken') || '';
		return await callCloudFunction('admin-manage', 'getUserList', {
			token,
			...params
		});
	},

	// 封禁用户
	async banUser(userId) {
		const token = uni.getStorageSync('adminToken') || '';
		return await callCloudFunction('admin-manage', 'banUser', {
			token,
			userId
		});
	},

	// 解封用户
	async unbanUser(userId) {
		const token = uni.getStorageSync('adminToken') || '';
		return await callCloudFunction('admin-manage', 'unbanUser', {
			token,
			userId
		});
	},

	// 获取统计数据
	async getStats() {
		const token = uni.getStorageSync('adminToken') || '';
		return await callCloudFunction('admin-manage', 'getStats', { token });
	}
};

// 工具函数
export const utils = {
	// 检查登录状态
	isLoggedIn() {
		return !!getUserToken();
	},

	// 获取用户信息
	getUserInfo() {
		return uni.getStorageSync('userInfo') || {};
	},

	// 清除登录信息
	clearUserData() {
		uni.removeStorageSync('userToken');
		uni.removeStorageSync('userInfo');
	},

	// 跳转到登录页面
	goToLogin() {
		uni.navigateTo({
			url: '/pages/userLogin/userLogin'
		});
	},

	// 格式化时间
	formatTime(timestamp) {
		const date = new Date(timestamp);
		const now = new Date();
		const diff = now - date;
		
		if (diff < 60 * 1000) {
			return '刚刚';
		} else if (diff < 60 * 60 * 1000) {
			return `${Math.floor(diff / (60 * 1000))}分钟前`;
		} else if (diff < 24 * 60 * 60 * 1000) {
			return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;
		} else if (diff < 2 * 24 * 60 * 60 * 1000) {
			return '昨天';
		} else {
			return `${date.getMonth() + 1}-${date.getDate()}`;
		}
	},

	// 格式化价格
	formatPrice(price) {
		if (price >= 10000) {
			return `${(price / 10000).toFixed(1)}万`;
		}
		return price.toString();
	},

	// 显示错误信息
	showError(message) {
		uni.showToast({
			title: message || '操作失败',
			icon: 'none',
			duration: 2000
		});
	},

	// 显示成功信息
	showSuccess(message) {
		uni.showToast({
			title: message || '操作成功',
			icon: 'success',
			duration: 1500
		});
	},

	// 显示加载中
	showLoading(title = '加载中...') {
		uni.showLoading({
			title,
			mask: true
		});
	},

	// 隐藏加载中
	hideLoading() {
		uni.hideLoading();
	}
};
