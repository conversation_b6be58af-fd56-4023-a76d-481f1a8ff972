
.home-container.data-v-57280228 {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* Banner样式 */
.banner-section.data-v-57280228 {
	margin-bottom: 20rpx;
}
.banner-swiper.data-v-57280228 {
	height: 300rpx;
}
.banner-image.data-v-57280228 {
	width: 100%;
	height: 100%;
}

/* 搜索栏样式 */
.search-section.data-v-57280228 {
	padding: 0 30rpx 20rpx;
}
.search-bar.data-v-57280228 {
	display: flex;
	align-items: center;
	background-color: #fff;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.search-icon.data-v-57280228 {
	font-size: 32rpx;
	margin-right: 20rpx;
}
.search-placeholder.data-v-57280228 {
	color: #999;
	font-size: 28rpx;
}

/* 快捷入口样式 */
.quick-entry-section.data-v-57280228 {
	background-color: #fff;
	margin: 0 30rpx 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
}
.quick-entry-title.data-v-57280228 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}
.quick-entry-grid.data-v-57280228 {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 30rpx;
}
.quick-entry-item.data-v-57280228 {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
}
.quick-entry-icon.data-v-57280228 {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 15rpx;
}
.icon-text.data-v-57280228 {
	font-size: 40rpx;
}
.quick-entry-text.data-v-57280228 {
	font-size: 24rpx;
	color: #666;
}

/* 推荐房源样式 */
.recommend-section.data-v-57280228 {
	background-color: #fff;
	margin: 0 30rpx;
	border-radius: 20rpx;
	padding: 30rpx;
}
.section-header.data-v-57280228 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}
.section-title.data-v-57280228 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}
.section-more.data-v-57280228 {
	font-size: 26rpx;
	color: #007AFF;
}
.house-list.data-v-57280228 {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}
.house-item.data-v-57280228 {
	border-radius: 15rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.house-image.data-v-57280228 {
	width: 100%;
	height: 200rpx;
}
.house-info.data-v-57280228 {
	padding: 20rpx;
	background-color: #fff;
}
.house-title.data-v-57280228 {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.house-location.data-v-57280228 {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 15rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.house-bottom.data-v-57280228 {
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.house-price.data-v-57280228 {
	font-size: 28rpx;
	color: #FF3B30;
	font-weight: bold;
}
.house-type.data-v-57280228 {
	font-size: 24rpx;
	color: #666;
	background-color: #f8f9fa;
	padding: 5rpx 15rpx;
	border-radius: 10rpx;
}

/* 加载状态样式 */
.loading-more.data-v-57280228 {
	padding: 30rpx;
	text-align: center;
}
.loading-text.data-v-57280228 {
	color: #999;
	font-size: 28rpx;
}

