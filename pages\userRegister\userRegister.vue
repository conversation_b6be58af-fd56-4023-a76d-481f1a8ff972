<template>
	<view class="register-container">
		<view class="register-header">
			<view class="logo-placeholder">🏠</view>
			<text class="app-name">租房小助手</text>
			<text class="welcome-text">创建新账号</text>
		</view>

		<view class="register-form">
			<view class="form-item">
				<view class="input-wrapper">
					<text class="input-icon">👤</text>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入用户名（3-20位字符）" 
						v-model="formData.username"
						:maxlength="20"
					/>
				</view>
				<text class="input-tip" v-if="formData.username && !isValidUsername">用户名只能包含字母、数字和下划线</text>
			</view>

			<view class="form-item">
				<view class="input-wrapper">
					<text class="input-icon">📝</text>
					<input 
						class="form-input" 
						type="text" 
						placeholder="请输入昵称（可选）" 
						v-model="formData.nickname"
						:maxlength="50"
					/>
				</view>
			</view>

			<view class="form-item">
				<view class="input-wrapper">
					<text class="input-icon">📱</text>
					<input 
						class="form-input" 
						type="number" 
						placeholder="请输入手机号（可选）" 
						v-model="formData.phone"
						:maxlength="11"
					/>
				</view>
				<text class="input-tip" v-if="formData.phone && !isValidPhone">请输入正确的手机号</text>
			</view>

			<view class="form-item">
				<view class="input-wrapper">
					<text class="input-icon">🔒</text>
					<input 
						class="form-input" 
						:type="showPassword ? 'text' : 'password'" 
						placeholder="请输入密码（至少6位）" 
						v-model="formData.password"
						:maxlength="20"
					/>
					<text class="password-toggle" @click="togglePassword">
						{{ showPassword ? '👁️' : '🙈' }}
					</text>
				</view>
			</view>

			<view class="form-item">
				<view class="input-wrapper">
					<text class="input-icon">🔐</text>
					<input 
						class="form-input" 
						:type="showConfirmPassword ? 'text' : 'password'" 
						placeholder="请确认密码" 
						v-model="formData.confirmPassword"
						:maxlength="20"
					/>
					<text class="password-toggle" @click="toggleConfirmPassword">
						{{ showConfirmPassword ? '👁️' : '🙈' }}
					</text>
				</view>
				<text class="input-tip error" v-if="formData.confirmPassword && !isPasswordMatch">两次密码输入不一致</text>
			</view>

			<button 
				class="register-btn" 
				:class="{ 'btn-disabled': !canSubmit }"
				:disabled="!canSubmit || loading"
				@click="handleRegister"
			>
				{{ loading ? '注册中...' : '注册' }}
			</button>

			<view class="form-footer">
				<text class="login-link" @click="goToLogin">已有账号？立即登录</text>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			formData: {
				username: '',
				nickname: '',
				phone: '',
				password: '',
				confirmPassword: ''
			},
			showPassword: false,
			showConfirmPassword: false,
			loading: false
		}
	},
	computed: {
		// 验证用户名格式
		isValidUsername() {
			const username = this.formData.username.trim();
			if (!username) return true;
			return /^[a-zA-Z0-9_]{3,20}$/.test(username);
		},

		// 验证手机号格式
		isValidPhone() {
			const phone = this.formData.phone.trim();
			if (!phone) return true;
			return /^1[3-9]\d{9}$/.test(phone);
		},

		// 验证密码是否匹配
		isPasswordMatch() {
			if (!this.formData.confirmPassword) return true;
			return this.formData.password === this.formData.confirmPassword;
		},

		// 是否可以提交
		canSubmit() {
			return this.formData.username.trim().length >= 3 &&
				   this.formData.password.length >= 6 &&
				   this.isValidUsername &&
				   this.isPasswordMatch &&
				   (!this.formData.phone || this.isValidPhone);
		}
	},
	methods: {
		// 切换密码显示状态
		togglePassword() {
			this.showPassword = !this.showPassword;
		},

		// 切换确认密码显示状态
		toggleConfirmPassword() {
			this.showConfirmPassword = !this.showConfirmPassword;
		},

		// 处理注册
		async handleRegister() {
			if (!this.canSubmit) {
				uni.showToast({
					title: '请检查输入信息',
					icon: 'none'
				});
				return;
			}

			this.loading = true;

			try {
				const registerData = {
					username: this.formData.username.trim(),
					password: this.formData.password.trim()
				};

				// 添加可选字段
				if (this.formData.nickname.trim()) {
					registerData.nickname = this.formData.nickname.trim();
				}
				if (this.formData.phone.trim()) {
					registerData.phone = this.formData.phone.trim();
				}

				const res = await uniCloud.callFunction({
					name: 'user-auth',
					data: {
						action: 'register',
						data: registerData
					}
				});

				if (res.result.code === 200) {
					// 保存用户信息和token
					const userData = res.result.data;
					uni.setStorageSync('userToken', userData.token);
					uni.setStorageSync('userInfo', {
						userId: userData.userId,
						username: userData.username,
						nickname: userData.nickname
					});

					uni.showToast({
						title: '注册成功',
						icon: 'success'
					});

					// 延迟跳转到首页
					setTimeout(() => {
						uni.switchTab({
							url: '/pages/index/index'
						});
					}, 1500);

				} else {
					uni.showToast({
						title: res.result.message || '注册失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('注册失败:', error);
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'none'
				});
			} finally {
				this.loading = false;
			}
		},

		// 跳转到登录页面
		goToLogin() {
			uni.navigateBack();
		}
	}
}
</script>

<style scoped>
.register-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 80rpx 60rpx 60rpx;
	display: flex;
	flex-direction: column;
}

.register-header {
	text-align: center;
	margin-bottom: 80rpx;
}

.logo-placeholder {
	width: 120rpx;
	height: 120rpx;
	margin-bottom: 30rpx;
	font-size: 80rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.app-name {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: #fff;
	margin-bottom: 20rpx;
}

.welcome-text {
	display: block;
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
}

.register-form {
	flex: 1;
}

.form-item {
	margin-bottom: 40rpx;
}

.input-wrapper {
	position: relative;
	background-color: rgba(255, 255, 255, 0.9);
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.input-icon {
	font-size: 32rpx;
	margin-right: 20rpx;
	color: #666;
}

.form-input {
	flex: 1;
	height: 100rpx;
	font-size: 32rpx;
	color: #333;
}

.password-toggle {
	font-size: 32rpx;
	color: #666;
	padding: 10rpx;
}

.input-tip {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	margin-top: 10rpx;
	margin-left: 30rpx;
}

.input-tip.error {
	color: #FFD700;
}

.register-btn {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(45deg, #FF6B6B, #FF8E53);
	border: none;
	border-radius: 50rpx;
	color: #fff;
	font-size: 32rpx;
	font-weight: bold;
	margin-top: 40rpx;
	box-shadow: 0 8rpx 30rpx rgba(255, 107, 107, 0.3);
}

.register-btn:active {
	transform: translateY(2rpx);
}

.btn-disabled {
	background: #ccc !important;
	box-shadow: none !important;
}

.form-footer {
	text-align: center;
	margin-top: 60rpx;
}

.login-link {
	color: rgba(255, 255, 255, 0.9);
	font-size: 28rpx;
	text-decoration: underline;
}
</style>
