<template>
	<view class="home-container">
		<!-- Banner轮播图 -->
		<view class="banner-section">
			<swiper class="banner-swiper" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="500">
				<swiper-item v-for="(banner, index) in bannerList" :key="index">
					<image :src="banner.image" class="banner-image" mode="aspectFill"></image>
				</swiper-item>
			</swiper>
		</view>

		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-bar" @click="goToSearch">
				<text class="search-icon">🔍</text>
				<text class="search-placeholder">搜索房源、位置、关键词</text>
			</view>
		</view>

		<!-- 快捷入口 -->
		<view class="quick-entry-section">
			<view class="quick-entry-title">快捷入口</view>
			<view class="quick-entry-grid">
				<view class="quick-entry-item" v-for="(item, index) in quickEntryList" :key="index" @click="handleQuickEntry(item)">
					<view class="quick-entry-icon" :style="{backgroundColor: item.bgColor}">
						<text class="icon-text">{{ item.icon }}</text>
					</view>
					<text class="quick-entry-text">{{ item.text }}</text>
				</view>
			</view>
		</view>

		<!-- 推荐房源 -->
		<view class="recommend-section">
			<view class="section-header">
				<text class="section-title">推荐房源</text>
				<text class="section-more" @click="goToHouseList">查看更多</text>
			</view>
			<view class="house-list">
				<view class="house-item" v-for="(house, index) in recommendHouses" :key="index" @click="goToHouseDetail(house._id)">
					<image :src="house.images[0] || 'https://via.placeholder.com/400x300/CCCCCC/FFFFFF?text=暂无图片'" class="house-image" mode="aspectFill"></image>
					<view class="house-info">
						<text class="house-title">{{ house.title }}</text>
						<text class="house-location">{{ house.location.address }}</text>
						<view class="house-bottom">
							<text class="house-price">¥{{ house.price }}/月</text>
							<text class="house-type">{{ house.type }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-more" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// Banner轮播数据
			bannerList: [
				{
					image: '/static/banner1.jpg',
					title: '优质房源推荐'
				},
				{
					image: '/static/banner2.jpg',
					title: '安全租房保障'
				},
				{
					image: '/static/banner3.jpg',
					title: '便民服务'
				}
			],
			// 快捷入口数据
			quickEntryList: [
				{
					icon: '📍',
					text: '地图找房',
					bgColor: '#E3F2FD',
					action: 'map'
				},
				{
					icon: '❤️',
					text: '我的收藏',
					bgColor: '#FCE4EC',
					action: 'favorites'
				},
				{
					icon: '💬',
					text: '联系客服',
					bgColor: '#E8F5E8',
					action: 'contact'
				},
				{
					icon: '📖',
					text: '租房指南',
					bgColor: '#FFF3E0',
					action: 'guide'
				}
			],
			// 推荐房源数据
			recommendHouses: [],
			loading: false
		}
	},
	onLoad() {
		this.loadRecommendHouses();
	},
	onPullDownRefresh() {
		this.loadRecommendHouses();
	},
	methods: {
		// 加载推荐房源
		async loadRecommendHouses() {
			this.loading = true;

			try {
				const res = await uniCloud.callFunction({
					name: 'house-manage',
					data: {
						action: 'getHouseList',
						data: {
							page: 1,
							pageSize: 6,
							sortBy: 'created_at',
							sortOrder: 'desc'
						}
					}
				});

				if (res.result.code === 200) {
					this.recommendHouses = res.result.data.list;
				} else {
					uni.showToast({
						title: res.result.message || '加载失败',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('加载推荐房源失败:', error);
				uni.showToast({
					title: '网络错误',
					icon: 'none'
				});
			} finally {
				this.loading = false;
				uni.stopPullDownRefresh();
			}
		},

		// 跳转到搜索页面
		goToSearch() {
			uni.navigateTo({
				url: '/pages/houseList/houseList?search=true'
			});
		},

		// 处理快捷入口点击
		handleQuickEntry(item) {
			switch (item.action) {
				case 'map':
					uni.showToast({
						title: '地图找房功能开发中',
						icon: 'none'
					});
					break;
				case 'favorites':
					// 检查登录状态
					const token = uni.getStorageSync('userToken');
					if (!token) {
						uni.navigateTo({
							url: '/pages/userLogin/userLogin'
						});
						return;
					}
					uni.switchTab({
						url: '/pages/my/my'
					});
					break;
				case 'contact':
					uni.showModal({
						title: '联系客服',
						content: '客服电话：400-123-4567\n工作时间：9:00-18:00',
						showCancel: false
					});
					break;
				case 'guide':
					uni.showToast({
						title: '租房指南功能开发中',
						icon: 'none'
					});
					break;
			}
		},

		// 跳转到房源列表
		goToHouseList() {
			uni.switchTab({
				url: '/pages/houseList/houseList'
			});
		},

		// 跳转到房源详情
		goToHouseDetail(houseId) {
			uni.navigateTo({
				url: `/pages/houseDetail/houseDetail?id=${houseId}`
			});
		}
	}
}
</script>

<style scoped>
.home-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* Banner样式 */
.banner-section {
	margin-bottom: 20rpx;
}

.banner-swiper {
	height: 300rpx;
}

.banner-image {
	width: 100%;
	height: 100%;
}

/* 搜索栏样式 */
.search-section {
	padding: 0 30rpx 20rpx;
}

.search-bar {
	display: flex;
	align-items: center;
	background-color: #fff;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.search-icon {
	font-size: 32rpx;
	margin-right: 20rpx;
}

.search-placeholder {
	color: #999;
	font-size: 28rpx;
}

/* 快捷入口样式 */
.quick-entry-section {
	background-color: #fff;
	margin: 0 30rpx 20rpx;
	border-radius: 20rpx;
	padding: 30rpx;
}

.quick-entry-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 30rpx;
}

.quick-entry-grid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 30rpx;
}

.quick-entry-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 20rpx;
}

.quick-entry-icon {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 15rpx;
}

.icon-text {
	font-size: 40rpx;
}

.quick-entry-text {
	font-size: 24rpx;
	color: #666;
}

/* 推荐房源样式 */
.recommend-section {
	background-color: #fff;
	margin: 0 30rpx;
	border-radius: 20rpx;
	padding: 30rpx;
}

.section-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.section-more {
	font-size: 26rpx;
	color: #007AFF;
}

.house-list {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.house-item {
	border-radius: 15rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.house-image {
	width: 100%;
	height: 200rpx;
}

.house-info {
	padding: 20rpx;
	background-color: #fff;
}

.house-title {
	font-size: 28rpx;
	color: #333;
	font-weight: bold;
	display: block;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.house-location {
	font-size: 24rpx;
	color: #999;
	display: block;
	margin-bottom: 15rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.house-bottom {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.house-price {
	font-size: 28rpx;
	color: #FF3B30;
	font-weight: bold;
}

.house-type {
	font-size: 24rpx;
	color: #666;
	background-color: #f8f9fa;
	padding: 5rpx 15rpx;
	border-radius: 10rpx;
}

/* 加载状态样式 */
.loading-more {
	padding: 30rpx;
	text-align: center;
}

.loading-text {
	color: #999;
	font-size: 28rpx;
}
</style>
