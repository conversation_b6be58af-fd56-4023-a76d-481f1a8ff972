
.post-house-container.data-v-28698208 {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding: 20rpx 30rpx 40rpx;
}

/* 表单区块样式 */
.form-section.data-v-28698208 {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.section-title.data-v-28698208 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
}

/* 输入框样式 */
.input-wrapper.data-v-28698208 {
	border: 2rpx solid #eee;
	border-radius: 15rpx;
	padding: 0 20rpx;
}
.form-input.data-v-28698208 {
	height: 80rpx;
	font-size: 28rpx;
	color: #333;
	width: 100%;
}
.textarea-wrapper.data-v-28698208 {
	border: 2rpx solid #eee;
	border-radius: 15rpx;
	padding: 20rpx;
}
.form-textarea.data-v-28698208 {
	width: 100%;
	min-height: 200rpx;
	font-size: 28rpx;
	color: #333;
}

/* 图片上传样式 */
.image-upload.data-v-28698208 {
}
.image-list.data-v-28698208 {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
	margin-bottom: 20rpx;
}
.image-item.data-v-28698208 {
	position: relative;
	aspect-ratio: 1;
	border-radius: 15rpx;
	overflow: hidden;
}
.uploaded-image.data-v-28698208 {
	width: 100%;
	height: 100%;
}
.image-delete.data-v-28698208 {
	position: absolute;
	top: 10rpx;
	right: 10rpx;
	width: 40rpx;
	height: 40rpx;
	background-color: rgba(0, 0, 0, 0.6);
	color: #fff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
}
.image-add.data-v-28698208 {
	aspect-ratio: 1;
	border: 2rpx dashed #ddd;
	border-radius: 15rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	color: #999;
}
.add-icon.data-v-28698208 {
	font-size: 48rpx;
	margin-bottom: 10rpx;
}
.add-text.data-v-28698208 {
	font-size: 24rpx;
}
.image-tip.data-v-28698208 {
	font-size: 24rpx;
	color: #999;
}

/* 价格输入样式 */
.price-wrapper.data-v-28698208 {
	display: flex;
	align-items: center;
	border: 2rpx solid #eee;
	border-radius: 15rpx;
	padding: 0 20rpx;
}
.price-input.data-v-28698208 {
	flex: 1;
	height: 80rpx;
	font-size: 28rpx;
	color: #333;
}
.price-unit.data-v-28698208 {
	font-size: 28rpx;
	color: #666;
}

/* 房型选择样式 */
.type-selector.data-v-28698208 {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 20rpx;
}
.type-item.data-v-28698208 {
	padding: 20rpx;
	text-align: center;
	background-color: #f8f9fa;
	border-radius: 15rpx;
	font-size: 26rpx;
	color: #666;
}
.type-item.active.data-v-28698208 {
	background-color: #007AFF;
	color: #fff;
}

/* 配置选择样式 */
.config-selector.data-v-28698208 {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 15rpx;
}
.config-item.data-v-28698208 {
	padding: 15rpx;
	text-align: center;
	background-color: #f8f9fa;
	border-radius: 15rpx;
	font-size: 24rpx;
	color: #666;
}
.config-item.active.data-v-28698208 {
	background-color: #E3F2FD;
	color: #007AFF;
}

/* 位置选择样式 */
.location-wrapper.data-v-28698208 {
}
.location-input.data-v-28698208 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border: 2rpx solid #eee;
	border-radius: 15rpx;
	padding: 20rpx;
	min-height: 80rpx;
}
.location-text.data-v-28698208 {
	font-size: 28rpx;
	color: #333;
}
.location-placeholder.data-v-28698208 {
	font-size: 28rpx;
	color: #999;
}
.location-icon.data-v-28698208 {
	font-size: 32rpx;
}
.location-actions.data-v-28698208 {
	display: flex;
	gap: 20rpx;
	margin-top: 20rpx;
}
.location-btn.data-v-28698208 {
	flex: 1;
	height: 70rpx;
	border: none;
	border-radius: 35rpx;
	font-size: 26rpx;
	background-color: #007AFF;
	color: #fff;
}
.location-btn.secondary.data-v-28698208 {
	background-color: #f8f9fa;
	color: #666;
}

/* 联系方式样式 */
.contact-wrapper.data-v-28698208 {
}
.contact-item.data-v-28698208 {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.contact-item.data-v-28698208:last-child {
	margin-bottom: 0;
}
.contact-label.data-v-28698208 {
	width: 120rpx;
	font-size: 28rpx;
	color: #333;
}
.contact-input.data-v-28698208 {
	flex: 1;
	height: 80rpx;
	border: 2rpx solid #eee;
	border-radius: 15rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
	color: #333;
}

/* 提交按钮样式 */
.submit-section.data-v-28698208 {
	text-align: center;
	margin-top: 40rpx;
}
.submit-btn.data-v-28698208 {
	width: 100%;
	height: 100rpx;
	background-color: #007AFF;
	color: #fff;
	border: none;
	border-radius: 50rpx;
	font-size: 32rpx;
	font-weight: bold;
	margin-bottom: 20rpx;
}
.submit-btn.disabled.data-v-28698208 {
	background-color: #ccc;
}
.submit-tip.data-v-28698208 {
	font-size: 24rpx;
	color: #999;
	display: block;
}

