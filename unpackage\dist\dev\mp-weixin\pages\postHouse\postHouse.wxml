<view class="post-house-container data-v-28698208"><form data-event-opts="{{[['submit',[['handleSubmit',['$event']]]]]}}" bindsubmit="__e" class="data-v-28698208"><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">房源标题 *</view><view class="input-wrapper data-v-28698208"><input class="form-input data-v-28698208" type="text" placeholder="请输入房源标题（5-100字）" maxlength="{{100}}" data-event-opts="{{[['input',[['__set_model',['$0','title','$event',[]],['formData']]]]]}}" value="{{formData.title}}" bindinput="__e"/></view></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">房源描述</view><view class="textarea-wrapper data-v-28698208"><textarea class="form-textarea data-v-28698208" placeholder="请详细描述房源情况，如装修情况、周边环境等" maxlength="{{1000}}" show-confirm-bar="{{false}}" data-event-opts="{{[['input',[['__set_model',['$0','desc','$event',[]],['formData']]]]]}}" value="{{formData.desc}}" bindinput="__e"></textarea></view></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">房源图片</view><view class="image-upload data-v-28698208"><view class="image-list data-v-28698208"><block wx:for="{{formData.images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view class="image-item data-v-28698208"><image class="uploaded-image data-v-28698208" src="{{image}}" mode="aspectFill"></image><view data-event-opts="{{[['tap',[['removeImage',[index]]]]]}}" class="image-delete data-v-28698208" bindtap="__e">✕</view></view></block><block wx:if="{{$root.g0<10}}"><view data-event-opts="{{[['tap',[['chooseImage',['$event']]]]]}}" class="image-add data-v-28698208" bindtap="__e"><text class="add-icon data-v-28698208">📷</text><text class="add-text data-v-28698208">添加图片</text></view></block></view><text class="image-tip data-v-28698208">最多上传10张图片</text></view></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">租金价格 *</view><view class="price-wrapper data-v-28698208"><input class="price-input data-v-28698208" type="number" placeholder="请输入月租金" data-event-opts="{{[['input',[['__set_model',['$0','price','$event',[]],['formData']]]]]}}" value="{{formData.price}}" bindinput="__e"/><text class="price-unit data-v-28698208">元/月</text></view></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">房型 *</view><view class="type-selector data-v-28698208"><block wx:for="{{houseTypes}}" wx:for-item="type" wx:for-index="__i0__" wx:key="*this"><view data-event-opts="{{[['tap',[['selectType',['$0'],[[['houseTypes','',__i0__]]]]]]]}}" class="{{['type-item','data-v-28698208',(formData.type===type)?'active':'']}}" bindtap="__e">{{''+type+''}}</view></block></view></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">房屋配置</view><view class="config-selector data-v-28698208"><block wx:for="{{$root.l0}}" wx:for-item="config" wx:for-index="__i1__" wx:key="$orig"><view data-event-opts="{{[['tap',[['toggleConfig',['$0'],[[['houseConfigs','',__i1__]]]]]]]}}" class="{{['config-item','data-v-28698208',(config.g1)?'active':'']}}" bindtap="__e">{{''+config.$orig+''}}</view></block></view></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">详细地址 *</view><view class="location-wrapper data-v-28698208"><view data-event-opts="{{[['tap',[['chooseLocation',['$event']]]]]}}" class="location-input data-v-28698208" bindtap="__e"><block wx:if="{{formData.location.address}}"><text class="location-text data-v-28698208">{{formData.location.address}}</text></block><block wx:else><text class="location-placeholder data-v-28698208">点击选择位置</text></block><text class="location-icon data-v-28698208">📍</text></view><view class="location-actions data-v-28698208"><button data-event-opts="{{[['tap',[['chooseLocation',['$event']]]]]}}" class="location-btn data-v-28698208" bindtap="__e">选择位置</button><button data-event-opts="{{[['tap',[['showManualAddressInput',['$event']]]]]}}" class="location-btn secondary data-v-28698208" bindtap="__e">手动输入</button></view></view></view><view class="form-section data-v-28698208"><view class="section-title data-v-28698208">联系方式 *</view><view class="contact-wrapper data-v-28698208"><view class="contact-item data-v-28698208"><text class="contact-label data-v-28698208">手机号：</text><input class="contact-input data-v-28698208" type="number" placeholder="请输入手机号" maxlength="{{11}}" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['formData.contact']]]]]}}" value="{{formData.contact.phone}}" bindinput="__e"/></view><view class="contact-item data-v-28698208"><text class="contact-label data-v-28698208">微信号：</text><input class="contact-input data-v-28698208" type="text" placeholder="请输入微信号（可选）" maxlength="{{50}}" data-event-opts="{{[['input',[['__set_model',['$0','wechat','$event',[]],['formData.contact']]]]]}}" value="{{formData.contact.wechat}}" bindinput="__e"/></view></view></view><view class="submit-section data-v-28698208"><button class="{{['submit-btn','data-v-28698208',(!canSubmit)?'disabled':'']}}" disabled="{{!canSubmit||loading}}" data-event-opts="{{[['tap',[['handleSubmit',['$event']]]]]}}" bindtap="__e">{{''+(loading?'发布中...':'发布房源')+''}}</button><text class="submit-tip data-v-28698208">发布后需要管理员审核通过才能展示</text></view></form></view>