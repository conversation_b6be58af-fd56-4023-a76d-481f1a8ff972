<view class="register-container data-v-5723e274"><view class="register-header data-v-5723e274"><view class="logo-placeholder data-v-5723e274">🏠</view><text class="app-name data-v-5723e274">租房小助手</text><text class="welcome-text data-v-5723e274">创建新账号</text></view><view class="register-form data-v-5723e274"><view class="form-item data-v-5723e274"><view class="input-wrapper data-v-5723e274"><text class="input-icon data-v-5723e274">👤</text><input class="form-input data-v-5723e274" type="text" placeholder="请输入用户名（3-20位字符）" maxlength="{{20}}" data-event-opts="{{[['input',[['__set_model',['$0','username','$event',[]],['formData']]]]]}}" value="{{formData.username}}" bindinput="__e"/></view><block wx:if="{{formData.username&&!isValidUsername}}"><text class="input-tip data-v-5723e274">用户名只能包含字母、数字和下划线</text></block></view><view class="form-item data-v-5723e274"><view class="input-wrapper data-v-5723e274"><text class="input-icon data-v-5723e274">📝</text><input class="form-input data-v-5723e274" type="text" placeholder="请输入昵称（可选）" maxlength="{{50}}" data-event-opts="{{[['input',[['__set_model',['$0','nickname','$event',[]],['formData']]]]]}}" value="{{formData.nickname}}" bindinput="__e"/></view></view><view class="form-item data-v-5723e274"><view class="input-wrapper data-v-5723e274"><text class="input-icon data-v-5723e274">📱</text><input class="form-input data-v-5723e274" type="number" placeholder="请输入手机号（可选）" maxlength="{{11}}" data-event-opts="{{[['input',[['__set_model',['$0','phone','$event',[]],['formData']]]]]}}" value="{{formData.phone}}" bindinput="__e"/></view><block wx:if="{{formData.phone&&!isValidPhone}}"><text class="input-tip data-v-5723e274">请输入正确的手机号</text></block></view><view class="form-item data-v-5723e274"><view class="input-wrapper data-v-5723e274"><text class="input-icon data-v-5723e274">🔒</text><input class="form-input data-v-5723e274" type="{{showPassword?'text':'password'}}" placeholder="请输入密码（至少6位）" maxlength="{{20}}" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" value="{{formData.password}}" bindinput="__e"/><text data-event-opts="{{[['tap',[['togglePassword',['$event']]]]]}}" class="password-toggle data-v-5723e274" bindtap="__e">{{''+(showPassword?'👁️':'🙈')+''}}</text></view></view><view class="form-item data-v-5723e274"><view class="input-wrapper data-v-5723e274"><text class="input-icon data-v-5723e274">🔐</text><input class="form-input data-v-5723e274" type="{{showConfirmPassword?'text':'password'}}" placeholder="请确认密码" maxlength="{{20}}" data-event-opts="{{[['input',[['__set_model',['$0','confirmPassword','$event',[]],['formData']]]]]}}" value="{{formData.confirmPassword}}" bindinput="__e"/><text data-event-opts="{{[['tap',[['toggleConfirmPassword',['$event']]]]]}}" class="password-toggle data-v-5723e274" bindtap="__e">{{''+(showConfirmPassword?'👁️':'🙈')+''}}</text></view><block wx:if="{{formData.confirmPassword&&!isPasswordMatch}}"><text class="input-tip error data-v-5723e274">两次密码输入不一致</text></block></view><button class="{{['register-btn','data-v-5723e274',(!canSubmit)?'btn-disabled':'']}}" disabled="{{!canSubmit||loading}}" data-event-opts="{{[['tap',[['handleRegister',['$event']]]]]}}" bindtap="__e">{{''+(loading?'注册中...':'注册')+''}}</button><view class="form-footer data-v-5723e274"><text data-event-opts="{{[['tap',[['goToLogin',['$event']]]]]}}" class="login-link data-v-5723e274" bindtap="__e">已有账号？立即登录</text></view></view></view>