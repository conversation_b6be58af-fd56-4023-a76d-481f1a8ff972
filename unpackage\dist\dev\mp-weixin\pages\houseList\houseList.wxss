
.house-list-container.data-v-5fd6dcf8 {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 搜索栏样式 */
.search-section.data-v-5fd6dcf8 {
	background-color: #fff;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #eee;
}
.search-bar.data-v-5fd6dcf8 {
	display: flex;
	align-items: center;
	background-color: #f8f9fa;
	border-radius: 50rpx;
	padding: 0 30rpx;
}
.search-input.data-v-5fd6dcf8 {
	flex: 1;
	height: 80rpx;
	font-size: 28rpx;
	color: #333;
}
.search-btn.data-v-5fd6dcf8 {
	background-color: #007AFF;
	color: #fff;
	border: none;
	border-radius: 40rpx;
	padding: 15rpx 30rpx;
	font-size: 26rpx;
	margin-left: 20rpx;
}

/* 筛选栏样式 */
.filter-section.data-v-5fd6dcf8 {
	background-color: #fff;
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #eee;
}
.filter-scroll.data-v-5fd6dcf8 {
	flex: 1;
	white-space: nowrap;
}
.filter-item.data-v-5fd6dcf8 {
	display: inline-block;
	padding: 15rpx 30rpx;
	margin: 0 15rpx;
	background-color: #f8f9fa;
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #666;
}
.filter-item.active.data-v-5fd6dcf8 {
	background-color: #007AFF;
	color: #fff;
}
.filter-more.data-v-5fd6dcf8 {
	padding: 15rpx 30rpx;
	margin-right: 30rpx;
	background-color: #f8f9fa;
	border-radius: 30rpx;
	font-size: 26rpx;
	color: #666;
}

/* 房源列表样式 */
.house-list.data-v-5fd6dcf8 {
	padding: 20rpx 30rpx;
}
.house-item.data-v-5fd6dcf8 {
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	display: flex;
}
.house-image.data-v-5fd6dcf8 {
	width: 200rpx;
	height: 200rpx;
	flex-shrink: 0;
}
.house-info.data-v-5fd6dcf8 {
	flex: 1;
	padding: 20rpx;
	display: flex;
	flex-direction: column;
}
.house-title.data-v-5fd6dcf8 {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.house-desc.data-v-5fd6dcf8 {
	font-size: 26rpx;
	color: #666;
	margin-bottom: 15rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.house-tags.data-v-5fd6dcf8 {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 15rpx;
}
.house-tag.data-v-5fd6dcf8 {
	background-color: #f0f0f0;
	color: #666;
	font-size: 22rpx;
	padding: 5rpx 15rpx;
	border-radius: 15rpx;
	margin-right: 10rpx;
	margin-bottom: 5rpx;
}
.house-location.data-v-5fd6dcf8 {
	display: flex;
	align-items: center;
	margin-bottom: 15rpx;
}
.location-icon.data-v-5fd6dcf8 {
	font-size: 24rpx;
	margin-right: 10rpx;
}
.location-text.data-v-5fd6dcf8 {
	font-size: 24rpx;
	color: #999;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.house-bottom.data-v-5fd6dcf8 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: auto;
}
.house-price.data-v-5fd6dcf8 {
	font-size: 32rpx;
	font-weight: bold;
	color: #FF3B30;
}
.house-time.data-v-5fd6dcf8 {
	font-size: 22rpx;
	color: #999;
}

/* 加载状态样式 */
.loading-section.data-v-5fd6dcf8 {
	padding: 40rpx;
	text-align: center;
}
.loading-text.data-v-5fd6dcf8 {
	color: #999;
	font-size: 28rpx;
}

/* 空状态样式 */
.empty-section.data-v-5fd6dcf8 {
	padding: 100rpx 40rpx;
	text-align: center;
}
.empty-icon.data-v-5fd6dcf8 {
	font-size: 120rpx;
	display: block;
	margin-bottom: 30rpx;
}
.empty-text.data-v-5fd6dcf8 {
	font-size: 32rpx;
	color: #666;
	display: block;
	margin-bottom: 15rpx;
}
.empty-tip.data-v-5fd6dcf8 {
	font-size: 26rpx;
	color: #999;
	display: block;
}

/* 筛选弹窗样式 */
.filter-modal.data-v-5fd6dcf8 {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}
.filter-content.data-v-5fd6dcf8 {
	background-color: #fff;
	border-radius: 30rpx 30rpx 0 0;
	padding: 40rpx 30rpx;
	width: 100%;
	max-height: 80vh;
}
.filter-header.data-v-5fd6dcf8 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}
.filter-title.data-v-5fd6dcf8 {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}
.filter-close.data-v-5fd6dcf8 {
	font-size: 40rpx;
	color: #999;
	padding: 10rpx;
}
.filter-group.data-v-5fd6dcf8 {
	margin-bottom: 40rpx;
}
.filter-group-title.data-v-5fd6dcf8 {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}
.price-range.data-v-5fd6dcf8 {
	display: flex;
	align-items: center;
}
.price-input.data-v-5fd6dcf8 {
	flex: 1;
	height: 80rpx;
	border: 1rpx solid #ddd;
	border-radius: 10rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
}
.price-separator.data-v-5fd6dcf8 {
	margin: 0 20rpx;
	color: #666;
}
.location-input.data-v-5fd6dcf8 {
	width: 100%;
	height: 80rpx;
	border: 1rpx solid #ddd;
	border-radius: 10rpx;
	padding: 0 20rpx;
	font-size: 28rpx;
}
.filter-actions.data-v-5fd6dcf8 {
	display: flex;
	gap: 20rpx;
	margin-top: 60rpx;
}
.filter-reset.data-v-5fd6dcf8 {
	flex: 1;
	height: 80rpx;
	background-color: #f8f9fa;
	color: #666;
	border: none;
	border-radius: 10rpx;
	font-size: 28rpx;
}
.filter-confirm.data-v-5fd6dcf8 {
	flex: 1;
	height: 80rpx;
	background-color: #007AFF;
	color: #fff;
	border: none;
	border-radius: 10rpx;
	font-size: 28rpx;
}

