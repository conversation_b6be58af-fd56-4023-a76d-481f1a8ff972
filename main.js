import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'

// 全局工具函数
Vue.prototype.$utils = {
  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;

    if (diff < 60 * 1000) {
      return '刚刚';
    } else if (diff < 60 * 60 * 1000) {
      return `${Math.floor(diff / (60 * 1000))}分钟前`;
    } else if (diff < 24 * 60 * 60 * 1000) {
      return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;
    } else if (diff < 2 * 24 * 60 * 60 * 1000) {
      return '昨天';
    } else {
      return `${date.getMonth() + 1}-${date.getDate()}`;
    }
  },

  // 格式化价格
  formatPrice(price) {
    if (price >= 10000) {
      return `${(price / 10000).toFixed(1)}万`;
    }
    return price.toString();
  },

  // 检查登录状态
  isLoggedIn() {
    return !!uni.getStorageSync('userToken');
  },

  // 显示错误信息
  showError(message) {
    uni.showToast({
      title: message || '操作失败',
      icon: 'none',
      duration: 2000
    });
  },

  // 显示成功信息
  showSuccess(message) {
    uni.showToast({
      title: message || '操作成功',
      icon: 'success',
      duration: 1500
    });
  }
};

Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)

  // 全局配置
  app.config.globalProperties.$utils = {
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp);
      const now = new Date();
      const diff = now - date;

      if (diff < 60 * 1000) {
        return '刚刚';
      } else if (diff < 60 * 60 * 1000) {
        return `${Math.floor(diff / (60 * 1000))}分钟前`;
      } else if (diff < 24 * 60 * 60 * 1000) {
        return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;
      } else if (diff < 2 * 24 * 60 * 60 * 1000) {
        return '昨天';
      } else {
        return `${date.getMonth() + 1}-${date.getDate()}`;
      }
    },

    // 格式化价格
    formatPrice(price) {
      if (price >= 10000) {
        return `${(price / 10000).toFixed(1)}万`;
      }
      return price.toString();
    },

    // 检查登录状态
    isLoggedIn() {
      return !!uni.getStorageSync('userToken');
    },

    // 显示错误信息
    showError(message) {
      uni.showToast({
        title: message || '操作失败',
        icon: 'none',
        duration: 2000
      });
    },

    // 显示成功信息
    showSuccess(message) {
      uni.showToast({
        title: message || '操作成功',
        icon: 'success',
        duration: 1500
      });
    }
  };

  return {
    app
  }
}
// #endif