<view class="login-container data-v-7264ae84"><view class="login-header data-v-7264ae84"><view class="logo-placeholder data-v-7264ae84">🏠</view><text class="app-name data-v-7264ae84">租房小助手</text><text class="welcome-text data-v-7264ae84">欢迎回来</text></view><view class="login-form data-v-7264ae84"><view class="form-item data-v-7264ae84"><view class="input-wrapper data-v-7264ae84"><text class="input-icon data-v-7264ae84">👤</text><input class="form-input data-v-7264ae84" type="text" placeholder="请输入用户名" maxlength="{{20}}" data-event-opts="{{[['input',[['__set_model',['$0','username','$event',[]],['formData']]]]]}}" value="{{formData.username}}" bindinput="__e"/></view></view><view class="form-item data-v-7264ae84"><view class="input-wrapper data-v-7264ae84"><text class="input-icon data-v-7264ae84">🔒</text><input class="form-input data-v-7264ae84" type="{{showPassword?'text':'password'}}" placeholder="请输入密码" maxlength="{{20}}" data-event-opts="{{[['input',[['__set_model',['$0','password','$event',[]],['formData']]]]]}}" value="{{formData.password}}" bindinput="__e"/><text data-event-opts="{{[['tap',[['togglePassword',['$event']]]]]}}" class="password-toggle data-v-7264ae84" bindtap="__e">{{''+(showPassword?'👁️':'🙈')+''}}</text></view></view><button class="{{['login-btn','data-v-7264ae84',(!canSubmit)?'btn-disabled':'']}}" disabled="{{!canSubmit||loading}}" data-event-opts="{{[['tap',[['handleLogin',['$event']]]]]}}" bindtap="__e">{{''+(loading?'登录中...':'登录')+''}}</button><view class="form-footer data-v-7264ae84"><text data-event-opts="{{[['tap',[['goToRegister',['$event']]]]]}}" class="register-link data-v-7264ae84" bindtap="__e">还没有账号？立即注册</text></view></view><view class="quick-login-tip data-v-7264ae84"><text class="tip-text data-v-7264ae84">测试账号：testuser</text><text class="tip-text data-v-7264ae84">测试密码：123456</text></view></view>