

















































/* 全局样式 */
page {
	background-color: #F5F5F5;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
	line-height: 1.6;
}
/* 重置默认样式 */
/* 通用工具类 */
.flex {
	display: flex;
}
.flex-center {
	display: flex;
	align-items: center;
	justify-content: center;
}
.flex-between {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.flex-column {
	display: flex;
	flex-direction: column;
}
.text-center {
	text-align: center;
}
.text-left {
	text-align: left;
}
.text-right {
	text-align: right;
}
.text-primary {
	color: #007AFF;
}
.text-success {
	color: #34C759;
}
.text-warning {
	color: #FF9500;
}
.text-error {
	color: #FF3B30;
}
.text-gray {
	color: #999999;
}
.bg-white {
	background-color: #FFFFFF;
}
.bg-gray {
	background-color: #F8F9FA;
}
.border-radius {
	border-radius: 15rpx;
}
.box-shadow {
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}
.margin-top {
	margin-top: 20rpx;
}
.margin-bottom {
	margin-bottom: 20rpx;
}
.padding {
	padding: 20rpx;
}
.padding-horizontal {
	padding-left: 20rpx;
	padding-right: 20rpx;
}
.padding-vertical {
	padding-top: 20rpx;
	padding-bottom: 20rpx;
}
/* 按钮样式 */
.btn-primary {
	background-color: #007AFF;
	color: #FFFFFF;
	border: none;
	border-radius: 50rpx;
	font-size: 28rpx;
	font-weight: bold;
	padding: 20rpx 40rpx;
}
.btn-secondary {
	background-color: #F8F9FA;
	color: #333333;
	border: none;
	border-radius: 50rpx;
	font-size: 28rpx;
	padding: 20rpx 40rpx;
}
.btn-outline {
	background-color: transparent;
	color: #007AFF;
	border: 2rpx solid #007AFF;
	border-radius: 50rpx;
	font-size: 28rpx;
	padding: 18rpx 40rpx;
}
/* 卡片样式 */
.card {
	background-color: #FFFFFF;
	border-radius: 20rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	padding: 30rpx;
	margin-bottom: 20rpx;
}
/* 列表项样式 */
.list-item {
	background-color: #FFFFFF;
	padding: 30rpx;
	border-bottom: 1rpx solid #EEEEEE;
	display: flex;
	align-items: center;
}
.list-item:last-child {
	border-bottom: none;
}
/* 标签样式 */
.tag {
	background-color: #F8F9FA;
	color: #666666;
	font-size: 24rpx;
	padding: 10rpx 20rpx;
	border-radius: 15rpx;
	display: inline-block;
}
.tag.primary {
	background-color: #E3F2FD;
	color: #007AFF;
}
.tag.success {
	background-color: #E8F5E8;
	color: #34C759;
}
.tag.warning {
	background-color: #FFF3E0;
	color: #FF9500;
}
.tag.error {
	background-color: #FCE4EC;
	color: #FF3B30;
}
/* 价格样式 */
.price {
	color: #FF3B30;
	font-weight: bold;
	font-size: 32rpx;
}
.price-small {
	color: #FF3B30;
	font-weight: bold;
	font-size: 28rpx;
}
/* 状态样式 */
.status-pending {
	color: #FF9500;
}
.status-approved {
	color: #34C759;
}
.status-rejected {
	color: #FF3B30;
}
/* 输入框样式 */
.input-wrapper {
	background-color: #FFFFFF;
	border: 2rpx solid #EEEEEE;
	border-radius: 15rpx;
	padding: 0 20rpx;
	display: flex;
	align-items: center;
	min-height: 80rpx;
}
.input-wrapper.focus {
	border-color: #007AFF;
}
/* 加载动画 */
@-webkit-keyframes loading {
0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
}
100% {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
}
}
@keyframes loading {
0% {
		-webkit-transform: rotate(0deg);
		        transform: rotate(0deg);
}
100% {
		-webkit-transform: rotate(360deg);
		        transform: rotate(360deg);
}
}
.loading {
	-webkit-animation: loading 1s linear infinite;
	        animation: loading 1s linear infinite;
}
/* 渐入动画 */
@-webkit-keyframes fadeIn {
from {
		opacity: 0;
		-webkit-transform: translateY(20rpx);
		        transform: translateY(20rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
@keyframes fadeIn {
from {
		opacity: 0;
		-webkit-transform: translateY(20rpx);
		        transform: translateY(20rpx);
}
to {
		opacity: 1;
		-webkit-transform: translateY(0);
		        transform: translateY(0);
}
}
.fade-in {
	-webkit-animation: fadeIn 0.3s ease-out;
	        animation: fadeIn 0.3s ease-out;
}

