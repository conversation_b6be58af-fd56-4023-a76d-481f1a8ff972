<view class="house-list-container data-v-5fd6dcf8"><view class="search-section data-v-5fd6dcf8"><view class="search-bar data-v-5fd6dcf8"><input class="search-input data-v-5fd6dcf8" type="text" placeholder="搜索房源、位置、关键词" confirm-type="search" data-event-opts="{{[['confirm',[['handleSearch',['$event']]]],['input',[['__set_model',['','searchKeyword','$event',[]]]]]]}}" value="{{searchKeyword}}" bindconfirm="__e" bindinput="__e"/><button data-event-opts="{{[['tap',[['handleSearch',['$event']]]]]}}" class="search-btn data-v-5fd6dcf8" bindtap="__e">搜索</button></view></view><view class="filter-section data-v-5fd6dcf8"><scroll-view class="filter-scroll data-v-5fd6dcf8" scroll-x="true"><view data-event-opts="{{[['tap',[['selectType',['']]]]]}}" class="{{['filter-item','data-v-5fd6dcf8',(!filterData.type)?'active':'']}}" bindtap="__e">全部</view><block wx:for="{{houseTypes}}" wx:for-item="type" wx:for-index="__i0__" wx:key="*this"><view data-event-opts="{{[['tap',[['selectType',['$0'],[[['houseTypes','',__i0__]]]]]]]}}" class="{{['filter-item','data-v-5fd6dcf8',(filterData.type===type)?'active':'']}}" bindtap="__e">{{''+type+''}}</view></block></scroll-view><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" class="filter-more data-v-5fd6dcf8" bindtap="__e">筛选</view></view><view class="house-list data-v-5fd6dcf8"><block wx:for="{{$root.l1}}" wx:for-item="house" wx:for-index="index" wx:key="_id"><view data-event-opts="{{[['tap',[['goToHouseDetail',['$0'],[[['houseList','_id',house.$orig._id,'_id']]]]]]]}}" class="house-item data-v-5fd6dcf8" bindtap="__e"><image class="house-image data-v-5fd6dcf8" src="{{house.$orig.images[0]||'https://via.placeholder.com/400x300/CCCCCC/FFFFFF?text=暂无图片'}}" mode="aspectFill"></image><view class="house-info data-v-5fd6dcf8"><text class="house-title data-v-5fd6dcf8">{{house.$orig.title}}</text><text class="house-desc data-v-5fd6dcf8">{{house.$orig.desc}}</text><view class="house-tags data-v-5fd6dcf8"><text class="house-tag data-v-5fd6dcf8">{{house.$orig.type}}</text><block wx:for="{{house.l0}}" wx:for-item="config" wx:for-index="__i1__" wx:key="*this"><text class="house-tag data-v-5fd6dcf8">{{config}}</text></block></view><view class="house-location data-v-5fd6dcf8"><text class="location-icon data-v-5fd6dcf8">📍</text><text class="location-text data-v-5fd6dcf8">{{house.$orig.location.address}}</text></view><view class="house-bottom data-v-5fd6dcf8"><text class="house-price data-v-5fd6dcf8">{{"¥"+house.$orig.price+"/月"}}</text><text class="house-time data-v-5fd6dcf8">{{house.m0}}</text></view></view></view></block></view><block wx:if="{{loading}}"><view class="loading-section data-v-5fd6dcf8"><text class="loading-text data-v-5fd6dcf8">加载中...</text></view></block><block wx:if="{{$root.g0}}"><view class="empty-section data-v-5fd6dcf8"><text class="empty-icon data-v-5fd6dcf8">🏠</text><text class="empty-text data-v-5fd6dcf8">暂无房源信息</text><text class="empty-tip data-v-5fd6dcf8">试试调整筛选条件</text></view></block><block wx:if="{{showFilterModal}}"><view data-event-opts="{{[['tap',[['closeFilterModal',['$event']]]]]}}" class="filter-modal data-v-5fd6dcf8" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="filter-content data-v-5fd6dcf8" catchtap="__e"><view class="filter-header data-v-5fd6dcf8"><text class="filter-title data-v-5fd6dcf8">筛选条件</text><text data-event-opts="{{[['tap',[['closeFilterModal',['$event']]]]]}}" class="filter-close data-v-5fd6dcf8" bindtap="__e">✕</text></view><view class="filter-group data-v-5fd6dcf8"><text class="filter-group-title data-v-5fd6dcf8">价格范围</text><view class="price-range data-v-5fd6dcf8"><input class="price-input data-v-5fd6dcf8" type="number" placeholder="最低价" data-event-opts="{{[['input',[['__set_model',['$0','priceMin','$event',[]],['tempFilter']]]]]}}" value="{{tempFilter.priceMin}}" bindinput="__e"/><text class="price-separator data-v-5fd6dcf8">-</text><input class="price-input data-v-5fd6dcf8" type="number" placeholder="最高价" data-event-opts="{{[['input',[['__set_model',['$0','priceMax','$event',[]],['tempFilter']]]]]}}" value="{{tempFilter.priceMax}}" bindinput="__e"/></view></view><view class="filter-group data-v-5fd6dcf8"><text class="filter-group-title data-v-5fd6dcf8">地区</text><input class="location-input data-v-5fd6dcf8" type="text" placeholder="输入地区关键词" data-event-opts="{{[['input',[['__set_model',['$0','location','$event',[]],['tempFilter']]]]]}}" value="{{tempFilter.location}}" bindinput="__e"/></view><view class="filter-actions data-v-5fd6dcf8"><button data-event-opts="{{[['tap',[['resetFilter',['$event']]]]]}}" class="filter-reset data-v-5fd6dcf8" bindtap="__e">重置</button><button data-event-opts="{{[['tap',[['applyFilter',['$event']]]]]}}" class="filter-confirm data-v-5fd6dcf8" bindtap="__e">确定</button></view></view></view></block></view>