# 房屋租赁小程序云函数文档

## 概述

本项目包含房屋租赁小程序的完整云端功能，包括用户管理、房源管理、收藏功能和管理员功能。

## 数据库集合

### 1. user 集合（用户信息）
- `_id`: 用户ID
- `username`: 登录账号（唯一）
- `password`: 加密后的密码
- `nickname`: 用户昵称
- `avatar`: 头像URL
- `phone`: 手机号
- `is_banned`: 是否被封禁
- `created_at`: 注册时间
- `favorites`: 收藏的房源ID数组

### 2. house 集合（房源信息）
- `_id`: 房源ID
- `title`: 房源标题
- `desc`: 房源描述
- `images`: 房源图片URL数组
- `location`: 位置信息（经纬度+地址）
- `price`: 月租金
- `type`: 房型
- `config`: 房屋配置数组
- `contact`: 联系方式
- `owner_id`: 发布用户ID
- `status`: 审核状态（pending/approved/rejected）
- `created_at`: 创建时间
- `updated_at`: 更新时间

## 云函数列表

### 1. user-auth（用户认证）

#### 用户注册
```javascript
uniCloud.callFunction({
  name: 'user-auth',
  data: {
    action: 'register',
    data: {
      username: 'testuser',
      password: '123456',
      nickname: '测试用户',
      phone: '13800138000'
    }
  }
})
```

#### 用户登录
```javascript
uniCloud.callFunction({
  name: 'user-auth',
  data: {
    action: 'login',
    data: {
      username: 'testuser',
      password: '123456'
    }
  }
})
```

#### 获取用户信息
```javascript
uniCloud.callFunction({
  name: 'user-auth',
  data: {
    action: 'getUserInfo',
    data: {
      token: 'user_token_here'
    }
  }
})
```

#### 更新用户资料
```javascript
uniCloud.callFunction({
  name: 'user-auth',
  data: {
    action: 'updateProfile',
    data: {
      token: 'user_token_here',
      nickname: '新昵称',
      avatar: 'new_avatar_url',
      phone: '13900139000'
    }
  }
})
```

#### 修改密码
```javascript
uniCloud.callFunction({
  name: 'user-auth',
  data: {
    action: 'changePassword',
    data: {
      token: 'user_token_here',
      oldPassword: '123456',
      newPassword: '654321'
    }
  }
})
```

### 2. house-manage（房源管理）

#### 获取房源列表
```javascript
uniCloud.callFunction({
  name: 'house-manage',
  data: {
    action: 'getHouseList',
    data: {
      page: 1,
      pageSize: 10,
      priceMin: 2000,
      priceMax: 5000,
      type: '一居室',
      location: '朝阳区'
    }
  }
})
```

#### 获取房源详情
```javascript
uniCloud.callFunction({
  name: 'house-manage',
  data: {
    action: 'getHouseDetail',
    data: {
      houseId: 'house_id_here'
    }
  }
})
```

#### 发布房源
```javascript
uniCloud.callFunction({
  name: 'house-manage',
  data: {
    action: 'publishHouse',
    data: {
      token: 'user_token_here',
      houseData: {
        title: '精装一居室',
        desc: '房间干净整洁',
        images: ['image1.jpg', 'image2.jpg'],
        location: {
          latitude: 39.908823,
          longitude: 116.397470,
          address: '北京市朝阳区xxx'
        },
        price: 3500,
        type: '一居室',
        config: ['空调', '洗衣机'],
        contact: {
          phone: '13800138000',
          wechat: 'wechat123'
        }
      }
    }
  }
})
```

#### 搜索房源
```javascript
uniCloud.callFunction({
  name: 'house-manage',
  data: {
    action: 'searchHouses',
    data: {
      keyword: '一居室',
      page: 1,
      pageSize: 10
    }
  }
})
```

### 3. user-favorites（用户收藏）

#### 添加收藏
```javascript
uniCloud.callFunction({
  name: 'user-favorites',
  data: {
    action: 'addFavorite',
    data: {
      token: 'user_token_here',
      houseId: 'house_id_here'
    }
  }
})
```

#### 取消收藏
```javascript
uniCloud.callFunction({
  name: 'user-favorites',
  data: {
    action: 'removeFavorite',
    data: {
      token: 'user_token_here',
      houseId: 'house_id_here'
    }
  }
})
```

#### 获取收藏列表
```javascript
uniCloud.callFunction({
  name: 'user-favorites',
  data: {
    action: 'getFavorites',
    data: {
      token: 'user_token_here',
      page: 1,
      pageSize: 10
    }
  }
})
```

### 4. admin-manage（管理员功能）

#### 管理员登录
```javascript
uniCloud.callFunction({
  name: 'admin-manage',
  data: {
    action: 'adminLogin',
    data: {
      username: 'admin',
      password: 'admin123'
    }
  }
})
```

#### 审核房源
```javascript
uniCloud.callFunction({
  name: 'admin-manage',
  data: {
    action: 'auditHouse',
    data: {
      token: 'admin_token_here',
      houseId: 'house_id_here',
      status: 'approved', // 或 'rejected'
      reason: '驳回原因（可选）'
    }
  }
})
```

#### 获取统计数据
```javascript
uniCloud.callFunction({
  name: 'admin-manage',
  data: {
    action: 'getStats',
    data: {
      token: 'admin_token_here'
    }
  }
})
```

## 部署说明

1. 在 HBuilderX 中右键点击 `uniCloud-aliyun` 文件夹
2. 选择"上传所有云函数、公共模块及actions"
3. 等待上传完成
4. 在 uniCloud 控制台中配置数据库索引

## 安全说明

- 所有用户密码都经过 SHA256 加密存储
- 管理员密码使用 MD5 加密（默认账号：admin，密码：admin123）
- Token 有效期为 24 小时
- 数据库权限已配置，确保数据安全

## 注意事项

1. 首次使用需要在 uniCloud 控制台创建对应的数据库集合
2. 建议在生产环境中使用更安全的密码加密方式
3. 管理员账号信息建议存储在数据库中而非硬编码
4. 可根据实际需求调整 Token 有效期
