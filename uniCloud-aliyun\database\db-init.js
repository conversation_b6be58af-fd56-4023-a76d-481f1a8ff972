/**
 * 数据库初始化脚本
 * 在 uniCloud 控制台的云函数中运行此脚本来初始化数据库
 */

// 初始化数据库集合
async function initDatabase() {
  const db = uniCloud.database();
  
  try {
    console.log('开始初始化数据库...');
    
    // 创建 user 集合
    console.log('创建 user 集合...');
    const userCollection = db.collection('user');
    
    // 创建 house 集合  
    console.log('创建 house 集合...');
    const houseCollection = db.collection('house');
    
    // 创建测试用户
    console.log('创建测试用户...');
    const testUser = {
      username: 'testuser',
      password: 'e10adc3949ba59abbe56e057f20f883e', // 123456 的 MD5
      nickname: '测试用户',
      avatar: '',
      phone: '13800138000',
      is_banned: false,
      created_at: new Date(),
      favorites: []
    };
    
    // 检查测试用户是否已存在
    const existUser = await userCollection.where({ username: 'testuser' }).get();
    if (existUser.data.length === 0) {
      await userCollection.add(testUser);
      console.log('测试用户创建成功');
    } else {
      console.log('测试用户已存在');
    }
    
    // 创建测试房源
    console.log('创建测试房源...');
    const testHouses = [
      {
        title: '精装一居室，拎包入住',
        desc: '房间干净整洁，家具家电齐全，交通便利，适合上班族居住。周边配套设施完善，生活便利。',
        images: [
          'https://img.alicdn.com/imgextra/i1/6000000000000/O1CN01234567890_!!6000000000000-0-tps-750-500.jpg'
        ],
        location: {
          latitude: 39.908823,
          longitude: 116.397470,
          address: '北京市朝阳区建国门外大街1号'
        },
        price: 3500,
        type: '一居室',
        config: ['空调', '洗衣机', '冰箱', '热水器', '宽带'],
        contact: {
          phone: '13800138001',
          wechat: 'wechat123'
        },
        owner_id: 'test_user_id',
        status: 'approved',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: '温馨二居室，南北通透',
        desc: '房屋采光良好，南北通透，装修温馨，适合小家庭居住。小区环境优美，物业管理完善。',
        images: [
          'https://img.alicdn.com/imgextra/i2/6000000000000/O1CN01234567891_!!6000000000000-0-tps-750-500.jpg'
        ],
        location: {
          latitude: 39.918823,
          longitude: 116.407470,
          address: '北京市朝阳区三里屯街道'
        },
        price: 5800,
        type: '二居室',
        config: ['空调', '洗衣机', '冰箱', '热水器', '宽带', '电视', '沙发'],
        contact: {
          phone: '13800138002',
          wechat: 'wechat456'
        },
        owner_id: 'test_user_id',
        status: 'approved',
        created_at: new Date(),
        updated_at: new Date()
      },
      {
        title: '合租房间，价格实惠',
        desc: '合租房间，室友友好，价格实惠，适合年轻人居住。交通便利，生活设施齐全。',
        images: [
          'https://img.alicdn.com/imgextra/i3/6000000000000/O1CN01234567892_!!6000000000000-0-tps-750-500.jpg'
        ],
        location: {
          latitude: 39.928823,
          longitude: 116.417470,
          address: '北京市海淀区中关村大街'
        },
        price: 2200,
        type: '合租',
        config: ['空调', '洗衣机', '冰箱', '宽带', '独立卫生间'],
        contact: {
          phone: '13800138003',
          wechat: 'wechat789'
        },
        owner_id: 'test_user_id',
        status: 'approved',
        created_at: new Date(),
        updated_at: new Date()
      }
    ];
    
    // 检查测试房源是否已存在
    const existHouses = await houseCollection.where({ owner_id: 'test_user_id' }).get();
    if (existHouses.data.length === 0) {
      for (const house of testHouses) {
        await houseCollection.add(house);
      }
      console.log('测试房源创建成功');
    } else {
      console.log('测试房源已存在');
    }
    
    console.log('数据库初始化完成！');
    
    return {
      success: true,
      message: '数据库初始化成功'
    };
    
  } catch (error) {
    console.error('数据库初始化失败:', error);
    return {
      success: false,
      message: '数据库初始化失败',
      error: error.message
    };
  }
}

// 导出初始化函数
module.exports = {
  initDatabase
};
