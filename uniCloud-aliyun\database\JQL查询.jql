// 房屋租赁小程序数据库初始化和测试脚本
// 使用JQL语法操作项目关联的uniCloud空间的数据库

// ==================== 数据库集合初始化 ====================

// 1. 创建用户集合测试数据
// db.collection('user').add({
//   username: 'testuser',
//   password: 'hashed_password_here',
//   nickname: '测试用户',
//   avatar: '',
//   phone: '13800138000',
//   is_banned: false,
//   created_at: new Date(),
//   favorites: []
// });

// 2. 创建房源集合测试数据
// db.collection('house').add({
//   title: '精装一居室，拎包入住',
//   desc: '房间干净整洁，家具家电齐全，交通便利，适合上班族居住',
//   images: [
//     'https://example.com/image1.jpg',
//     'https://example.com/image2.jpg'
//   ],
//   location: {
//     latitude: 39.908823,
//     longitude: 116.397470,
//     address: '北京市东城区王府井大街1号'
//   },
//   price: 3500,
//   type: '一居室',
//   config: ['空调', '洗衣机', '冰箱', '热水器', '宽带'],
//   contact: {
//     phone: '13800138001',
//     wechat: 'wechat123'
//   },
//   owner_id: 'user_id_here',
//   status: 'approved',
//   created_at: new Date(),
//   updated_at: new Date()
// });

// ==================== 查询测试 ====================

// 查询所有用户
// db.collection('user').get();

// 查询所有房源
// db.collection('house').get();

// 查询已审核通过的房源
// db.collection('house').where({status: 'approved'}).get();

// 按价格范围查询房源
// db.collection('house').where({
//   price: db.command.gte(2000).and(db.command.lte(5000))
// }).get();

// 搜索房源标题
// db.collection('house').where({
//   title: new RegExp('一居室', 'i')
// }).get();

// ==================== 数据库初始化 ====================

// 创建测试用户（如果不存在）
// db.collection('user').add({
//   username: 'testuser',
//   password: 'e10adc3949ba59abbe56e057f20f883e', // 123456 的 MD5
//   nickname: '测试用户',
//   avatar: '',
//   phone: '13800138000',
//   is_banned: false,
//   created_at: new Date(),
//   favorites: []
// });

// 创建测试房源（如果不存在）
// db.collection('house').add({
//   title: '精装一居室，拎包入住',
//   desc: '房间干净整洁，家具家电齐全，交通便利，适合上班族居住',
//   images: ['https://via.placeholder.com/750x500/4CAF50/FFFFFF?text=精装一居室'],
//   location: {
//     latitude: 39.908823,
//     longitude: 116.397470,
//     address: '北京市朝阳区建国门外大街1号'
//   },
//   price: 3500,
//   type: '一居室',
//   config: ['空调', '洗衣机', '冰箱', '热水器', '宽带'],
//   contact: {
//     phone: '13800138001',
//     wechat: 'wechat123'
//   },
//   owner_id: 'test_user_id',
//   status: 'approved',
//   created_at: new Date(),
//   updated_at: new Date()
// });

// ==================== 数据统计查询 ====================

// 统计用户总数
// db.collection('user').count();

// 统计房源总数
// db.collection('house').count();

// 统计各状态房源数量
// db.collection('house').where({status: 'approved'}).count();
// db.collection('house').where({status: 'pending'}).count();
// db.collection('house').where({status: 'rejected'}).count();

// 默认查询：获取所有已审核房源
db.collection('house').where({status: 'approved'}).orderBy('created_at', 'desc').get();
