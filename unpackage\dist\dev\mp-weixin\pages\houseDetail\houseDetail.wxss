
.house-detail-container.data-v-6992ea2a {
	background-color: #f5f5f5;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 图片轮播样式 */
.image-section.data-v-6992ea2a {
	position: relative;
	height: 500rpx;
}
.image-swiper.data-v-6992ea2a {
	height: 100%;
}
.house-image.data-v-6992ea2a {
	width: 100%;
	height: 100%;
}
.image-count.data-v-6992ea2a {
	position: absolute;
	bottom: 20rpx;
	right: 20rpx;
	background-color: rgba(0, 0, 0, 0.6);
	color: #fff;
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
}

/* 基本信息样式 */
.info-section.data-v-6992ea2a {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.price-section.data-v-6992ea2a {
	display: flex;
	align-items: center;
	margin-bottom: 20rpx;
}
.price.data-v-6992ea2a {
	font-size: 48rpx;
	font-weight: bold;
	color: #FF3B30;
}
.price-unit.data-v-6992ea2a {
	font-size: 28rpx;
	color: #999;
	margin-left: 10rpx;
}
.favorite-btn.data-v-6992ea2a {
	margin-left: auto;
	padding: 10rpx;
}
.favorite-icon.data-v-6992ea2a {
	font-size: 48rpx;
}
.house-title.data-v-6992ea2a {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}
.house-tags.data-v-6992ea2a {
	display: flex;
	flex-wrap: wrap;
	margin-bottom: 20rpx;
}
.house-tag.data-v-6992ea2a {
	background-color: #f0f0f0;
	color: #666;
	font-size: 24rpx;
	padding: 10rpx 20rpx;
	border-radius: 20rpx;
	margin-right: 15rpx;
	margin-bottom: 10rpx;
}
.house-tag.primary.data-v-6992ea2a {
	background-color: #E3F2FD;
	color: #007AFF;
}
.location-section.data-v-6992ea2a {
	display: flex;
	align-items: center;
}
.location-icon.data-v-6992ea2a {
	font-size: 28rpx;
	margin-right: 10rpx;
}
.location-text.data-v-6992ea2a {
	font-size: 28rpx;
	color: #666;
}

/* 描述样式 */
.desc-section.data-v-6992ea2a {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.section-title.data-v-6992ea2a {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 20rpx;
	display: block;
}
.desc-text.data-v-6992ea2a {
	font-size: 28rpx;
	color: #666;
	line-height: 1.6;
	display: block;
}

/* 配置样式 */
.config-section.data-v-6992ea2a {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.config-grid.data-v-6992ea2a {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 30rpx;
}
.config-item.data-v-6992ea2a {
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
}
.config-icon.data-v-6992ea2a {
	font-size: 40rpx;
	margin-bottom: 10rpx;
}
.config-text.data-v-6992ea2a {
	font-size: 24rpx;
	color: #666;
}

/* 联系方式样式 */
.contact-section.data-v-6992ea2a {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.contact-item.data-v-6992ea2a {
	display: flex;
	align-items: center;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;
}
.contact-item.data-v-6992ea2a:last-child {
	border-bottom: none;
}
.contact-icon.data-v-6992ea2a {
	font-size: 32rpx;
	margin-right: 20rpx;
}
.contact-text.data-v-6992ea2a {
	flex: 1;
	font-size: 28rpx;
	color: #333;
}
.contact-btn.data-v-6992ea2a {
	background-color: #007AFF;
	color: #fff;
	border: none;
	border-radius: 30rpx;
	padding: 15rpx 30rpx;
	font-size: 24rpx;
}

/* 发布信息样式 */
.publish-section.data-v-6992ea2a {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}
.publish-time.data-v-6992ea2a,
.update-time.data-v-6992ea2a {
	font-size: 26rpx;
	color: #999;
	display: block;
	margin-bottom: 10rpx;
}

/* 底部操作栏样式 */
.bottom-actions.data-v-6992ea2a {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 20rpx 30rpx;
	border-top: 1rpx solid #eee;
	display: flex;
	gap: 20rpx;
	z-index: 100;
}
.action-btn.data-v-6992ea2a {
	flex: 1;
	height: 80rpx;
	border: none;
	border-radius: 40rpx;
	font-size: 28rpx;
	font-weight: bold;
}
.action-btn.primary.data-v-6992ea2a {
	background-color: #007AFF;
	color: #fff;
}
.action-btn.secondary.data-v-6992ea2a {
	background-color: #f8f9fa;
	color: #666;
}

/* 加载状态样式 */
.loading-section.data-v-6992ea2a {
	padding: 100rpx;
	text-align: center;
}
.loading-text.data-v-6992ea2a {
	color: #999;
	font-size: 28rpx;
}

