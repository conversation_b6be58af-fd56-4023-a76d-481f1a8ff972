{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端11/pages/postHouse/postHouse.vue?266a", "webpack:///D:/web/project/前端11/pages/postHouse/postHouse.vue?7093", "webpack:///D:/web/project/前端11/pages/postHouse/postHouse.vue?c706", "webpack:///D:/web/project/前端11/pages/postHouse/postHouse.vue?b9ed", "uni-app:///pages/postHouse/postHouse.vue", "webpack:///D:/web/project/前端11/pages/postHouse/postHouse.vue?f631", "webpack:///D:/web/project/前端11/pages/postHouse/postHouse.vue?3af5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "formData", "title", "desc", "images", "price", "type", "config", "location", "latitude", "longitude", "address", "contact", "phone", "wechat", "loading", "houseTypes", "houseConfigs", "computed", "canSubmit", "onLoad", "uni", "content", "success", "utils", "url", "methods", "chooseImage", "count", "sizeType", "sourceType", "fail", "removeImage", "selectType", "toggleConfig", "chooseLocation", "confirmText", "icon", "console", "showManualAddressInput", "editable", "placeholderText", "handleSubmit", "houseData", "houseManage", "res", "setTimeout", "resetForm"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACqC;;;AAG7F;AACyK;AACzK,gBAAgB,gLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAooB,CAAgB,koBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;AC2JxpB;AAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;UACAC;QACA;QACAC;UACAC;UACAC;QACA;MACA;MACAC;MAEA;MACAC;MAEA;MACAC,eACA,sCACA,qCACA;IAEA;EACA;EAEAC;IACAC;MACA,iDACA,uBACA,sBACA,mCACA;IACA;EACA;EAEAC;IACA;IACA;MACAC;QACAnB;QACAoB;QACAC;UACA;YACAC;UACA;YACAH;cACAI;YACA;UACA;QACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MACA;MAEAN;QACAO;QACAC;QACAC;QACAP;UACA;UACA;QACA;QACAQ;UACAP;QACA;MACA;IACA;IAEA;IACAQ;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAd;QACAE;UACA;YACA;YACAF;cACAnB;cACAoB;cACAc;cACAb;gBACA;kBACAF;gBACA;cACA;YACA;YACA;UACA;;UAEA;UACAA;YACAE;cACA;gBACAd;gBACAC;gBACAC;cACA;cACAU;gBACAnB;gBACAmC;cACA;YACA;YACAN;cACAO;cACA;gBACA;gBACA;cACA;gBACA;gBACAjB;kBACAnB;kBACAoB;kBACAc;kBACAb;oBACA;sBACA;oBACA;kBACA;gBACA;cACA;gBACA;gBACAF;kBACAnB;kBACAoB;kBACAc;kBACAb;oBACA;sBACA;oBACA;kBACA;gBACA;cACA;YACA;UACA;QACA;QACAQ;UACAV;YACAnB;YACAmC;UACA;QACA;MACA;IACA;IAEA;IACAE;MAAA;MACAlB;QACAnB;QACAsC;QACAC;QACAlB;UACA;YACA;cACAd;cACAC;cACAC;YACA;YACAU;cACAnB;cACAmC;YACA;UACA;QACA;MACA;IACA;IAEA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAlB;gBAAA;cAAA;gBAAA,MAKA;kBAAA;kBAAA;gBAAA;gBACAA;gBAAA;cAAA;gBAIA;gBAAA;gBAGAmB,4CACA;kBACAtC;gBAAA;gBAAA;gBAAA,OAGAuC;cAAA;gBAAAC;gBAEA;kBACArB;;kBAEA;kBACA;;kBAEA;kBACAsB;oBACAzB;sBACAI;oBACA;kBACA;gBACA;kBACAD;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAc;gBACAd;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAuB;MACA;QACA7C;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;UACAC;UACAC;UACAC;QACA;QACAC;UACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvaA;AAAA;AAAA;AAAA;AAA27B,CAAgB,q5BAAG,EAAC,C;;;;;;;;;;;ACA/8B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/postHouse/postHouse.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';\nimport Vue from 'vue'\nimport Page from './pages/postHouse/postHouse.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./postHouse.vue?vue&type=template&id=28698208&scoped=true&\"\nvar renderjs\nimport script from \"./postHouse.vue?vue&type=script&lang=js&\"\nexport * from \"./postHouse.vue?vue&type=script&lang=js&\"\nimport style0 from \"./postHouse.vue?vue&type=style&index=0&id=28698208&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"28698208\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/postHouse/postHouse.vue\"\nexport default component.exports", "export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./postHouse.vue?vue&type=template&id=28698208&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.formData.images.length\n  var l0 = _vm.__map(_vm.houseConfigs, function (config, __i1__) {\n    var $orig = _vm.__get_orig(config)\n    var g1 = _vm.formData.config.includes(config)\n    return {\n      $orig: $orig,\n      g1: g1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./postHouse.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./postHouse.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"post-house-container\">\n\t\t<form @submit=\"handleSubmit\">\n\t\t\t<!-- 房源标题 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">房源标题 *</view>\n\t\t\t\t<view class=\"input-wrapper\">\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"form-input\" \n\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\tplaceholder=\"请输入房源标题（5-100字）\" \n\t\t\t\t\t\tv-model=\"formData.title\"\n\t\t\t\t\t\t:maxlength=\"100\"\n\t\t\t\t\t/>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 房源描述 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">房源描述</view>\n\t\t\t\t<view class=\"textarea-wrapper\">\n\t\t\t\t\t<textarea \n\t\t\t\t\t\tclass=\"form-textarea\" \n\t\t\t\t\t\tplaceholder=\"请详细描述房源情况，如装修情况、周边环境等\" \n\t\t\t\t\t\tv-model=\"formData.desc\"\n\t\t\t\t\t\t:maxlength=\"1000\"\n\t\t\t\t\t\t:show-confirm-bar=\"false\"\n\t\t\t\t\t></textarea>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 房源图片 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">房源图片</view>\n\t\t\t\t<view class=\"image-upload\">\n\t\t\t\t\t<view class=\"image-list\">\n\t\t\t\t\t\t<view class=\"image-item\" v-for=\"(image, index) in formData.images\" :key=\"index\">\n\t\t\t\t\t\t\t<image :src=\"image\" class=\"uploaded-image\" mode=\"aspectFill\"></image>\n\t\t\t\t\t\t\t<view class=\"image-delete\" @click=\"removeImage(index)\">✕</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"image-add\" @click=\"chooseImage\" v-if=\"formData.images.length < 10\">\n\t\t\t\t\t\t\t<text class=\"add-icon\">📷</text>\n\t\t\t\t\t\t\t<text class=\"add-text\">添加图片</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t<text class=\"image-tip\">最多上传10张图片</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 租金价格 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">租金价格 *</view>\n\t\t\t\t<view class=\"price-wrapper\">\n\t\t\t\t\t<input \n\t\t\t\t\t\tclass=\"price-input\" \n\t\t\t\t\t\ttype=\"number\" \n\t\t\t\t\t\tplaceholder=\"请输入月租金\" \n\t\t\t\t\t\tv-model=\"formData.price\"\n\t\t\t\t\t/>\n\t\t\t\t\t<text class=\"price-unit\">元/月</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 房型选择 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">房型 *</view>\n\t\t\t\t<view class=\"type-selector\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tclass=\"type-item\" \n\t\t\t\t\t\t:class=\"{ active: formData.type === type }\"\n\t\t\t\t\t\tv-for=\"type in houseTypes\" \n\t\t\t\t\t\t:key=\"type\"\n\t\t\t\t\t\t@click=\"selectType(type)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{{ type }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 房屋配置 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">房屋配置</view>\n\t\t\t\t<view class=\"config-selector\">\n\t\t\t\t\t<view \n\t\t\t\t\t\tclass=\"config-item\" \n\t\t\t\t\t\t:class=\"{ active: formData.config.includes(config) }\"\n\t\t\t\t\t\tv-for=\"config in houseConfigs\" \n\t\t\t\t\t\t:key=\"config\"\n\t\t\t\t\t\t@click=\"toggleConfig(config)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t{{ config }}\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 详细地址 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">详细地址 *</view>\n\t\t\t\t<view class=\"location-wrapper\">\n\t\t\t\t\t<view class=\"location-input\" @click=\"chooseLocation\">\n\t\t\t\t\t\t<text class=\"location-text\" v-if=\"formData.location.address\">{{ formData.location.address }}</text>\n\t\t\t\t\t\t<text class=\"location-placeholder\" v-else>点击选择位置</text>\n\t\t\t\t\t\t<text class=\"location-icon\">📍</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"location-actions\">\n\t\t\t\t\t\t<button class=\"location-btn\" @click=\"chooseLocation\">选择位置</button>\n\t\t\t\t\t\t<button class=\"location-btn secondary\" @click=\"showManualAddressInput\">手动输入</button>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 联系方式 -->\n\t\t\t<view class=\"form-section\">\n\t\t\t\t<view class=\"section-title\">联系方式 *</view>\n\t\t\t\t<view class=\"contact-wrapper\">\n\t\t\t\t\t<view class=\"contact-item\">\n\t\t\t\t\t\t<text class=\"contact-label\">手机号：</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"contact-input\" \n\t\t\t\t\t\t\ttype=\"number\" \n\t\t\t\t\t\t\tplaceholder=\"请输入手机号\" \n\t\t\t\t\t\t\tv-model=\"formData.contact.phone\"\n\t\t\t\t\t\t\t:maxlength=\"11\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"contact-item\">\n\t\t\t\t\t\t<text class=\"contact-label\">微信号：</text>\n\t\t\t\t\t\t<input \n\t\t\t\t\t\t\tclass=\"contact-input\" \n\t\t\t\t\t\t\ttype=\"text\" \n\t\t\t\t\t\t\tplaceholder=\"请输入微信号（可选）\" \n\t\t\t\t\t\t\tv-model=\"formData.contact.wechat\"\n\t\t\t\t\t\t\t:maxlength=\"50\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 提交按钮 -->\n\t\t\t<view class=\"submit-section\">\n\t\t\t\t<button \n\t\t\t\t\tclass=\"submit-btn\" \n\t\t\t\t\t:class=\"{ disabled: !canSubmit }\"\n\t\t\t\t\t:disabled=\"!canSubmit || loading\"\n\t\t\t\t\t@click=\"handleSubmit\"\n\t\t\t\t>\n\t\t\t\t\t{{ loading ? '发布中...' : '发布房源' }}\n\t\t\t\t</button>\n\t\t\t\t<text class=\"submit-tip\">发布后需要管理员审核通过才能展示</text>\n\t\t\t</view>\n\t\t</form>\n\t</view>\n</template>\n\n<script>\nimport { houseManage, utils } from '@/utils/api.js';\n\nexport default {\n\tdata() {\n\t\treturn {\n\t\t\tformData: {\n\t\t\t\ttitle: '',\n\t\t\t\tdesc: '',\n\t\t\t\timages: [],\n\t\t\t\tprice: '',\n\t\t\t\ttype: '',\n\t\t\t\tconfig: [],\n\t\t\t\tlocation: {\n\t\t\t\t\tlatitude: '',\n\t\t\t\t\tlongitude: '',\n\t\t\t\t\taddress: ''\n\t\t\t\t},\n\t\t\t\tcontact: {\n\t\t\t\t\tphone: '',\n\t\t\t\t\twechat: ''\n\t\t\t\t}\n\t\t\t},\n\t\t\tloading: false,\n\t\t\t\n\t\t\t// 房型选项\n\t\t\thouseTypes: ['一居室', '二居室', '三居室', '四居室', '合租', '单间', '其他'],\n\t\t\t\n\t\t\t// 配置选项\n\t\t\thouseConfigs: [\n\t\t\t\t'空调', '洗衣机', '冰箱', '热水器', '宽带', '电视', \n\t\t\t\t'沙发', '床', '衣柜', '书桌', '微波炉', '燃气灶', \n\t\t\t\t'油烟机', '独立卫生间', '阳台', '停车位'\n\t\t\t]\n\t\t}\n\t},\n\t\n\tcomputed: {\n\t\tcanSubmit() {\n\t\t\treturn this.formData.title.trim().length >= 5 &&\n\t\t\t\t   this.formData.price &&\n\t\t\t\t   this.formData.type &&\n\t\t\t\t   this.formData.location.address &&\n\t\t\t\t   (this.formData.contact.phone || this.formData.contact.wechat);\n\t\t}\n\t},\n\t\n\tonLoad() {\n\t\t// 检查登录状态\n\t\tif (!utils.isLoggedIn()) {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '提示',\n\t\t\t\tcontent: '请先登录后再发布房源',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\tutils.goToLogin();\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\turl: '/pages/index/index'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t},\n\t\n\tmethods: {\n\t\t// 选择图片\n\t\tchooseImage() {\n\t\t\tconst remainCount = 10 - this.formData.images.length;\n\t\t\t\n\t\t\tuni.chooseImage({\n\t\t\t\tcount: remainCount,\n\t\t\t\tsizeType: ['compressed'],\n\t\t\t\tsourceType: ['album', 'camera'],\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t// 这里应该上传到云存储，暂时使用本地路径\n\t\t\t\t\tthis.formData.images = [...this.formData.images, ...res.tempFilePaths];\n\t\t\t\t},\n\t\t\t\tfail: () => {\n\t\t\t\t\tutils.showError('选择图片失败');\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 删除图片\n\t\tremoveImage(index) {\n\t\t\tthis.formData.images.splice(index, 1);\n\t\t},\n\t\t\n\t\t// 选择房型\n\t\tselectType(type) {\n\t\t\tthis.formData.type = type;\n\t\t},\n\t\t\n\t\t// 切换配置\n\t\ttoggleConfig(config) {\n\t\t\tconst index = this.formData.config.indexOf(config);\n\t\t\tif (index > -1) {\n\t\t\t\tthis.formData.config.splice(index, 1);\n\t\t\t} else {\n\t\t\t\tthis.formData.config.push(config);\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 选择位置\n\t\tchooseLocation() {\n\t\t\t// 先检查位置权限\n\t\t\tuni.getSetting({\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.authSetting['scope.userLocation'] === false) {\n\t\t\t\t\t\t// 用户拒绝了位置权限，引导用户去设置\n\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\ttitle: '位置权限',\n\t\t\t\t\t\t\tcontent: '需要获取您的位置信息来选择房源地址，请在设置中开启位置权限',\n\t\t\t\t\t\t\tconfirmText: '去设置',\n\t\t\t\t\t\t\tsuccess: (modalRes) => {\n\t\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\t\tuni.openSetting();\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\t// 有权限或未询问过，直接选择位置\n\t\t\t\t\tuni.chooseLocation({\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tthis.formData.location = {\n\t\t\t\t\t\t\t\tlatitude: res.latitude,\n\t\t\t\t\t\t\t\tlongitude: res.longitude,\n\t\t\t\t\t\t\t\taddress: res.address || res.name || '未知地址'\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '位置选择成功',\n\t\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('选择位置失败:', err);\n\t\t\t\t\t\t\tif (err.errMsg.includes('cancel')) {\n\t\t\t\t\t\t\t\t// 用户取消选择\n\t\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t\t} else if (err.errMsg.includes('auth')) {\n\t\t\t\t\t\t\t\t// 权限问题\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '位置权限',\n\t\t\t\t\t\t\t\t\tcontent: '需要获取您的位置信息来选择房源地址，请授权后重试',\n\t\t\t\t\t\t\t\t\tconfirmText: '重新授权',\n\t\t\t\t\t\t\t\t\tsuccess: (modalRes) => {\n\t\t\t\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\t\t\t\tthis.chooseLocation();\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// 其他错误，提供手动输入选项\n\t\t\t\t\t\t\t\tuni.showModal({\n\t\t\t\t\t\t\t\t\ttitle: '获取位置失败',\n\t\t\t\t\t\t\t\t\tcontent: '无法获取位置信息，是否手动输入地址？',\n\t\t\t\t\t\t\t\t\tconfirmText: '手动输入',\n\t\t\t\t\t\t\t\t\tsuccess: (modalRes) => {\n\t\t\t\t\t\t\t\t\t\tif (modalRes.confirm) {\n\t\t\t\t\t\t\t\t\t\t\tthis.showManualAddressInput();\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t\tfail: () => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '获取设置失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\n\t\t// 显示手动输入地址的弹窗\n\t\tshowManualAddressInput() {\n\t\t\tuni.showModal({\n\t\t\t\ttitle: '输入地址',\n\t\t\t\teditable: true,\n\t\t\t\tplaceholderText: '请输入详细地址',\n\t\t\t\tsuccess: (res) => {\n\t\t\t\t\tif (res.confirm && res.content.trim()) {\n\t\t\t\t\t\tthis.formData.location = {\n\t\t\t\t\t\t\tlatitude: '',\n\t\t\t\t\t\t\tlongitude: '',\n\t\t\t\t\t\t\taddress: res.content.trim()\n\t\t\t\t\t\t};\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '地址设置成功',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t},\n\t\t\n\t\t// 提交表单\n\t\tasync handleSubmit() {\n\t\t\tif (!this.canSubmit) {\n\t\t\t\tutils.showError('请填写完整信息');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\t// 验证手机号格式\n\t\t\tif (this.formData.contact.phone && !/^1[3-9]\\d{9}$/.test(this.formData.contact.phone)) {\n\t\t\t\tutils.showError('请输入正确的手机号');\n\t\t\t\treturn;\n\t\t\t}\n\t\t\t\n\t\t\tthis.loading = true;\n\t\t\t\n\t\t\ttry {\n\t\t\t\tconst houseData = {\n\t\t\t\t\t...this.formData,\n\t\t\t\t\tprice: parseInt(this.formData.price)\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\tconst res = await houseManage.publishHouse(houseData);\n\t\t\t\t\n\t\t\t\tif (res.code === 200) {\n\t\t\t\t\tutils.showSuccess('发布成功，等待审核');\n\t\t\t\t\t\n\t\t\t\t\t// 清空表单\n\t\t\t\t\tthis.resetForm();\n\t\t\t\t\t\n\t\t\t\t\t// 跳转到我的发布页面\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.switchTab({\n\t\t\t\t\t\t\turl: '/pages/my/my'\n\t\t\t\t\t\t});\n\t\t\t\t\t}, 1500);\n\t\t\t\t} else {\n\t\t\t\t\tutils.showError(res.message || '发布失败');\n\t\t\t\t}\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('发布房源失败:', error);\n\t\t\t\tutils.showError('网络错误');\n\t\t\t} finally {\n\t\t\t\tthis.loading = false;\n\t\t\t}\n\t\t},\n\t\t\n\t\t// 重置表单\n\t\tresetForm() {\n\t\t\tthis.formData = {\n\t\t\t\ttitle: '',\n\t\t\t\tdesc: '',\n\t\t\t\timages: [],\n\t\t\t\tprice: '',\n\t\t\t\ttype: '',\n\t\t\t\tconfig: [],\n\t\t\t\tlocation: {\n\t\t\t\t\tlatitude: '',\n\t\t\t\t\tlongitude: '',\n\t\t\t\t\taddress: ''\n\t\t\t\t},\n\t\t\t\tcontact: {\n\t\t\t\t\tphone: '',\n\t\t\t\t\twechat: ''\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\t}\n}\n</script>\n\n<style scoped>\n.post-house-container {\n\tbackground-color: #f5f5f5;\n\tmin-height: 100vh;\n\tpadding: 20rpx 30rpx 40rpx;\n}\n\n/* 表单区块样式 */\n.form-section {\n\tbackground-color: #fff;\n\tborder-radius: 20rpx;\n\tpadding: 30rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.section-title {\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tcolor: #333;\n\tmargin-bottom: 20rpx;\n}\n\n/* 输入框样式 */\n.input-wrapper {\n\tborder: 2rpx solid #eee;\n\tborder-radius: 15rpx;\n\tpadding: 0 20rpx;\n}\n\n.form-input {\n\theight: 80rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n\twidth: 100%;\n}\n\n.textarea-wrapper {\n\tborder: 2rpx solid #eee;\n\tborder-radius: 15rpx;\n\tpadding: 20rpx;\n}\n\n.form-textarea {\n\twidth: 100%;\n\tmin-height: 200rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n/* 图片上传样式 */\n.image-upload {\n\t\n}\n\n.image-list {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(3, 1fr);\n\tgap: 20rpx;\n\tmargin-bottom: 20rpx;\n}\n\n.image-item {\n\tposition: relative;\n\taspect-ratio: 1;\n\tborder-radius: 15rpx;\n\toverflow: hidden;\n}\n\n.uploaded-image {\n\twidth: 100%;\n\theight: 100%;\n}\n\n.image-delete {\n\tposition: absolute;\n\ttop: 10rpx;\n\tright: 10rpx;\n\twidth: 40rpx;\n\theight: 40rpx;\n\tbackground-color: rgba(0, 0, 0, 0.6);\n\tcolor: #fff;\n\tborder-radius: 50%;\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: center;\n\tfont-size: 24rpx;\n}\n\n.image-add {\n\taspect-ratio: 1;\n\tborder: 2rpx dashed #ddd;\n\tborder-radius: 15rpx;\n\tdisplay: flex;\n\tflex-direction: column;\n\talign-items: center;\n\tjustify-content: center;\n\tcolor: #999;\n}\n\n.add-icon {\n\tfont-size: 48rpx;\n\tmargin-bottom: 10rpx;\n}\n\n.add-text {\n\tfont-size: 24rpx;\n}\n\n.image-tip {\n\tfont-size: 24rpx;\n\tcolor: #999;\n}\n\n/* 价格输入样式 */\n.price-wrapper {\n\tdisplay: flex;\n\talign-items: center;\n\tborder: 2rpx solid #eee;\n\tborder-radius: 15rpx;\n\tpadding: 0 20rpx;\n}\n\n.price-input {\n\tflex: 1;\n\theight: 80rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.price-unit {\n\tfont-size: 28rpx;\n\tcolor: #666;\n}\n\n/* 房型选择样式 */\n.type-selector {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(3, 1fr);\n\tgap: 20rpx;\n}\n\n.type-item {\n\tpadding: 20rpx;\n\ttext-align: center;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 15rpx;\n\tfont-size: 26rpx;\n\tcolor: #666;\n}\n\n.type-item.active {\n\tbackground-color: #007AFF;\n\tcolor: #fff;\n}\n\n/* 配置选择样式 */\n.config-selector {\n\tdisplay: grid;\n\tgrid-template-columns: repeat(3, 1fr);\n\tgap: 15rpx;\n}\n\n.config-item {\n\tpadding: 15rpx;\n\ttext-align: center;\n\tbackground-color: #f8f9fa;\n\tborder-radius: 15rpx;\n\tfont-size: 24rpx;\n\tcolor: #666;\n}\n\n.config-item.active {\n\tbackground-color: #E3F2FD;\n\tcolor: #007AFF;\n}\n\n/* 位置选择样式 */\n.location-wrapper {\n\t\n}\n\n.location-input {\n\tdisplay: flex;\n\talign-items: center;\n\tjustify-content: space-between;\n\tborder: 2rpx solid #eee;\n\tborder-radius: 15rpx;\n\tpadding: 20rpx;\n\tmin-height: 80rpx;\n}\n\n.location-text {\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.location-placeholder {\n\tfont-size: 28rpx;\n\tcolor: #999;\n}\n\n.location-icon {\n\tfont-size: 32rpx;\n}\n\n.location-actions {\n\tdisplay: flex;\n\tgap: 20rpx;\n\tmargin-top: 20rpx;\n}\n\n.location-btn {\n\tflex: 1;\n\theight: 70rpx;\n\tborder: none;\n\tborder-radius: 35rpx;\n\tfont-size: 26rpx;\n\tbackground-color: #007AFF;\n\tcolor: #fff;\n}\n\n.location-btn.secondary {\n\tbackground-color: #f8f9fa;\n\tcolor: #666;\n}\n\n/* 联系方式样式 */\n.contact-wrapper {\n\t\n}\n\n.contact-item {\n\tdisplay: flex;\n\talign-items: center;\n\tmargin-bottom: 20rpx;\n}\n\n.contact-item:last-child {\n\tmargin-bottom: 0;\n}\n\n.contact-label {\n\twidth: 120rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n.contact-input {\n\tflex: 1;\n\theight: 80rpx;\n\tborder: 2rpx solid #eee;\n\tborder-radius: 15rpx;\n\tpadding: 0 20rpx;\n\tfont-size: 28rpx;\n\tcolor: #333;\n}\n\n/* 提交按钮样式 */\n.submit-section {\n\ttext-align: center;\n\tmargin-top: 40rpx;\n}\n\n.submit-btn {\n\twidth: 100%;\n\theight: 100rpx;\n\tbackground-color: #007AFF;\n\tcolor: #fff;\n\tborder: none;\n\tborder-radius: 50rpx;\n\tfont-size: 32rpx;\n\tfont-weight: bold;\n\tmargin-bottom: 20rpx;\n}\n\n.submit-btn.disabled {\n\tbackground-color: #ccc;\n}\n\n.submit-tip {\n\tfont-size: 24rpx;\n\tcolor: #999;\n\tdisplay: block;\n}\n</style>\n", "import mod from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./postHouse.vue?vue&type=style&index=0&id=28698208&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./postHouse.vue?vue&type=style&index=0&id=28698208&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754093677258\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}