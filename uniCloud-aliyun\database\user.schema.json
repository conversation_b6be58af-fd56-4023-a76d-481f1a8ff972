{"bsonType": "object", "description": "用户信息集合", "required": [], "properties": {"_id": {"description": "用户ID，系统自动生成"}, "username": {"bsonType": "string", "description": "用户登录账号，唯一", "minLength": 3, "maxLength": 20, "pattern": "^[a-zA-Z0-9_]+$", "title": "用户名"}, "password": {"bsonType": "string", "description": "哈希后的密码", "minLength": 6, "title": "密码"}, "nickname": {"bsonType": "string", "description": "用户昵称", "maxLength": 50, "title": "昵称"}, "avatar": {"bsonType": "string", "description": "头像URL", "title": "头像"}, "phone": {"bsonType": "string", "description": "手机号", "pattern": "^1[3-9]\\d{9}$", "title": "手机号"}, "is_banned": {"bsonType": "bool", "description": "是否被封禁", "default": false, "title": "封禁状态"}, "created_at": {"bsonType": "timestamp", "description": "注册时间", "forceDefaultValue": {"$env": "now"}, "title": "注册时间"}, "favorites": {"bsonType": "array", "description": "收藏的房源ID数组", "items": {"bsonType": "string"}, "default": [], "title": "收藏列表"}}, "permission": {"read": "auth.uid == doc._id || 'admin' in auth.role", "create": true, "update": "auth.uid == doc._id || 'admin' in auth.role", "delete": "'admin' in auth.role"}}