<view class="my-container data-v-0be17cc6"><view class="user-section data-v-0be17cc6"><block wx:if="{{isLoggedIn}}"><view class="user-info data-v-0be17cc6"><block wx:if="{{!userInfo.avatar}}"><view class="user-avatar data-v-0be17cc6">👤</view></block><block wx:else><image class="user-avatar data-v-0be17cc6" src="{{userInfo.avatar}}" mode="aspectFill"></image></block><view class="user-details data-v-0be17cc6"><text class="user-nickname data-v-0be17cc6">{{userInfo.nickname||userInfo.username}}</text><text class="user-username data-v-0be17cc6">{{"@"+userInfo.username}}</text></view><view class="user-actions data-v-0be17cc6"><button data-event-opts="{{[['tap',[['goToEditProfile',['$event']]]]]}}" class="edit-btn data-v-0be17cc6" bindtap="__e">编辑</button></view></view></block><block wx:else><view class="login-prompt data-v-0be17cc6"><view class="user-avatar data-v-0be17cc6">👤</view><view class="login-info data-v-0be17cc6"><text class="login-text data-v-0be17cc6">点击登录</text><text class="login-tip data-v-0be17cc6">登录后可发布和收藏房源</text></view><button data-event-opts="{{[['tap',[['goToLogin',['$event']]]]]}}" class="login-btn data-v-0be17cc6" bindtap="__e">登录</button></view></block></view><view class="menu-section data-v-0be17cc6"><view data-event-opts="{{[['tap',[['goToMyHouses',['$event']]]]]}}" class="menu-item data-v-0be17cc6" bindtap="__e"><text class="menu-icon data-v-0be17cc6">🏠</text><text class="menu-text data-v-0be17cc6">我的发布</text><block wx:if="{{myHousesCount>0}}"><text class="menu-count data-v-0be17cc6">{{myHousesCount}}</text></block><text class="menu-arrow data-v-0be17cc6">></text></view><view data-event-opts="{{[['tap',[['goToMyFavorites',['$event']]]]]}}" class="menu-item data-v-0be17cc6" bindtap="__e"><text class="menu-icon data-v-0be17cc6">❤️</text><text class="menu-text data-v-0be17cc6">我的收藏</text><block wx:if="{{favoritesCount>0}}"><text class="menu-count data-v-0be17cc6">{{favoritesCount}}</text></block><text class="menu-arrow data-v-0be17cc6">></text></view><view data-event-opts="{{[['tap',[['goToSettings',['$event']]]]]}}" class="menu-item data-v-0be17cc6" bindtap="__e"><text class="menu-icon data-v-0be17cc6">⚙️</text><text class="menu-text data-v-0be17cc6">设置</text><text class="menu-arrow data-v-0be17cc6">></text></view></view><view class="other-section data-v-0be17cc6"><view data-event-opts="{{[['tap',[['contactService',['$event']]]]]}}" class="menu-item data-v-0be17cc6" bindtap="__e"><text class="menu-icon data-v-0be17cc6">💬</text><text class="menu-text data-v-0be17cc6">联系客服</text><text class="menu-arrow data-v-0be17cc6">></text></view><view data-event-opts="{{[['tap',[['showAbout',['$event']]]]]}}" class="menu-item data-v-0be17cc6" bindtap="__e"><text class="menu-icon data-v-0be17cc6">ℹ️</text><text class="menu-text data-v-0be17cc6">关于我们</text><text class="menu-arrow data-v-0be17cc6">></text></view></view><block wx:if="{{isLoggedIn}}"><view class="logout-section data-v-0be17cc6"><button data-event-opts="{{[['tap',[['handleLogout',['$event']]]]]}}" class="logout-btn data-v-0be17cc6" bindtap="__e">退出登录</button></view></block><block wx:if="{{showMyHousesModal}}"><view data-event-opts="{{[['tap',[['closeMyHousesModal',['$event']]]]]}}" class="modal data-v-0be17cc6" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content data-v-0be17cc6" catchtap="__e"><view class="modal-header data-v-0be17cc6"><text class="modal-title data-v-0be17cc6">我的发布</text><text data-event-opts="{{[['tap',[['closeMyHousesModal',['$event']]]]]}}" class="modal-close data-v-0be17cc6" bindtap="__e">✕</text></view><scroll-view class="modal-body data-v-0be17cc6" scroll-y="true"><block wx:for="{{$root.l0}}" wx:for-item="house" wx:for-index="__i0__" wx:key="_id"><view data-event-opts="{{[['tap',[['goToHouseDetail',['$0'],[[['myHouses','_id',house.$orig._id,'_id']]]]]]]}}" class="house-item data-v-0be17cc6" bindtap="__e"><image class="house-image data-v-0be17cc6" src="{{house.$orig.images[0]||'https://via.placeholder.com/240x180/CCCCCC/FFFFFF?text=暂无图片'}}" mode="aspectFill"></image><view class="house-info data-v-0be17cc6"><text class="house-title data-v-0be17cc6">{{house.$orig.title}}</text><text class="house-price data-v-0be17cc6">{{"¥"+house.$orig.price+"/月"}}</text><view class="house-status data-v-0be17cc6"><text class="{{['status-text','data-v-0be17cc6','status-'+house.$orig.status]}}">{{''+house.m0+''}}</text></view></view></view></block><block wx:if="{{$root.g0===0}}"><view class="empty-tip data-v-0be17cc6">暂无发布的房源</view></block></scroll-view></view></view></block><block wx:if="{{showFavoritesModal}}"><view data-event-opts="{{[['tap',[['closeFavoritesModal',['$event']]]]]}}" class="modal data-v-0be17cc6" bindtap="__e"><view data-event-opts="{{[['tap',[['',['$event']]]]]}}" class="modal-content data-v-0be17cc6" catchtap="__e"><view class="modal-header data-v-0be17cc6"><text class="modal-title data-v-0be17cc6">我的收藏</text><text data-event-opts="{{[['tap',[['closeFavoritesModal',['$event']]]]]}}" class="modal-close data-v-0be17cc6" bindtap="__e">✕</text></view><scroll-view class="modal-body data-v-0be17cc6" scroll-y="true"><block wx:for="{{favorites}}" wx:for-item="house" wx:for-index="__i1__" wx:key="_id"><view data-event-opts="{{[['tap',[['goToHouseDetail',['$0'],[[['favorites','_id',house._id,'_id']]]]]]]}}" class="house-item data-v-0be17cc6" bindtap="__e"><image class="house-image data-v-0be17cc6" src="{{house.images[0]||'https://via.placeholder.com/240x180/CCCCCC/FFFFFF?text=暂无图片'}}" mode="aspectFill"></image><view class="house-info data-v-0be17cc6"><text class="house-title data-v-0be17cc6">{{house.title}}</text><text class="house-price data-v-0be17cc6">{{"¥"+house.price+"/月"}}</text><text class="house-location data-v-0be17cc6">{{house.location.address}}</text></view></view></block><block wx:if="{{$root.g1===0}}"><view class="empty-tip data-v-0be17cc6">暂无收藏的房源</view></block></scroll-view></view></view></block></view>