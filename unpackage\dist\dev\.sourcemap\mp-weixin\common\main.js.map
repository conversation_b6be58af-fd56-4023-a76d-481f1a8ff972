{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/web/project/前端11/App.vue?6cbc", "webpack:///D:/web/project/前端11/App.vue?5559", "uni-app:///App.vue", "webpack:///D:/web/project/前端11/App.vue?1432", "webpack:///D:/web/project/前端11/App.vue?9f7f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "prototype", "$utils", "formatTime", "timestamp", "date", "Date", "now", "diff", "Math", "floor", "getMonth", "getDate", "formatPrice", "price", "toFixed", "toString", "isLoggedIn", "uni", "getStorageSync", "showError", "message", "showToast", "title", "icon", "duration", "showSuccess", "config", "productionTip", "App", "mpType", "app", "$mount", "onLaunch", "console", "onShow", "onHide", "methods", "checkLoginStatus", "initGlobalConfig"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAAuE;AAGlI;AACA;AAAgC;AAAA;AALhC;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAM1D;AACAC,YAAG,CAACC,SAAS,CAACC,MAAM,GAAG;EACrB;EACAC,UAAU,sBAACC,SAAS,EAAE;IACpB,IAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;IAChC,IAAMG,GAAG,GAAG,IAAID,IAAI,EAAE;IACtB,IAAME,IAAI,GAAGD,GAAG,GAAGF,IAAI;IAEvB,IAAIG,IAAI,GAAG,EAAE,GAAG,IAAI,EAAE;MACpB,OAAO,IAAI;IACb,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;MAChC,iBAAUC,IAAI,CAACC,KAAK,CAACF,IAAI,IAAI,EAAE,GAAG,IAAI,CAAC,CAAC;IAC1C,CAAC,MAAM,IAAIA,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;MACrC,iBAAUC,IAAI,CAACC,KAAK,CAACF,IAAI,IAAI,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC/C,CAAC,MAAM,IAAIA,IAAI,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE;MACzC,OAAO,IAAI;IACb,CAAC,MAAM;MACL,iBAAUH,IAAI,CAACM,QAAQ,EAAE,GAAG,CAAC,cAAIN,IAAI,CAACO,OAAO,EAAE;IACjD;EACF,CAAC;EAED;EACAC,WAAW,uBAACC,KAAK,EAAE;IACjB,IAAIA,KAAK,IAAI,KAAK,EAAE;MAClB,iBAAU,CAACA,KAAK,GAAG,KAAK,EAAEC,OAAO,CAAC,CAAC,CAAC;IACtC;IACA,OAAOD,KAAK,CAACE,QAAQ,EAAE;EACzB,CAAC;EAED;EACAC,UAAU,wBAAG;IACX,OAAO,CAAC,CAACC,GAAG,CAACC,cAAc,CAAC,WAAW,CAAC;EAC1C,CAAC;EAED;EACAC,SAAS,qBAACC,OAAO,EAAE;IACjBH,GAAG,CAACI,SAAS,CAAC;MACZC,KAAK,EAAEF,OAAO,IAAI,MAAM;MACxBG,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;EAED;EACAC,WAAW,uBAACL,OAAO,EAAE;IACnBH,GAAG,CAACI,SAAS,CAAC;MACZC,KAAK,EAAEF,OAAO,IAAI,MAAM;MACxBG,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ;AACF,CAAC;AAEDzB,YAAG,CAAC2B,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAI/B,YAAG,mBACd6B,YAAG,EACN;AACF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;AClEZ;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACa;;;AAG/D;AACmK;AACnK,gBAAgB,gLAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAgmB,CAAgB,4nBAAG,EAAC,C;;;;;;;;;;;;;;;;;;eCCpnB;EACAC;IACAC;;IAEA;IACA;;IAEA;IACA;EACA;EACAC;IACAD;EACA;EACAE;IACAF;EACA;EACAG;IACA;IACAC;MACA;MACA;MAEA;QACAJ;MACA;QACAA;MACA;IACA;IAEA;IACAK;MACA;MACArB;;MAEA;MACAA;QACA;UACAA;YACAK;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAAm3B,CAAgB,u3BAAG,EAAC,C;;;;;;;;;;;ACAv4B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/vue-cli-plugin-uni/packages/uni-cloud/dist/index.js';import App from './App'\n\n\nimport Vue from 'vue'\nimport './uni.promisify.adaptor'\n\n// 全局工具函数\nVue.prototype.$utils = {\n  // 格式化时间\n  formatTime(timestamp) {\n    const date = new Date(timestamp);\n    const now = new Date();\n    const diff = now - date;\n\n    if (diff < 60 * 1000) {\n      return '刚刚';\n    } else if (diff < 60 * 60 * 1000) {\n      return `${Math.floor(diff / (60 * 1000))}分钟前`;\n    } else if (diff < 24 * 60 * 60 * 1000) {\n      return `${Math.floor(diff / (60 * 60 * 1000))}小时前`;\n    } else if (diff < 2 * 24 * 60 * 60 * 1000) {\n      return '昨天';\n    } else {\n      return `${date.getMonth() + 1}-${date.getDate()}`;\n    }\n  },\n\n  // 格式化价格\n  formatPrice(price) {\n    if (price >= 10000) {\n      return `${(price / 10000).toFixed(1)}万`;\n    }\n    return price.toString();\n  },\n\n  // 检查登录状态\n  isLoggedIn() {\n    return !!uni.getStorageSync('userToken');\n  },\n\n  // 显示错误信息\n  showError(message) {\n    uni.showToast({\n      title: message || '操作失败',\n      icon: 'none',\n      duration: 2000\n    });\n  },\n\n  // 显示成功信息\n  showSuccess(message) {\n    uni.showToast({\n      title: message || '操作成功',\n      icon: 'success',\n      duration: 1500\n    });\n  }\n};\n\nVue.config.productionTip = false\nApp.mpType = 'app'\nconst app = new Vue({\n  ...App\n})\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\nexport default {\r\n\tonLaunch: function() {\r\n\t\tconsole.log('租房小助手启动');\r\n\r\n\t\t// 检查登录状态\r\n\t\tthis.checkLoginStatus();\r\n\r\n\t\t// 初始化全局配置\r\n\t\tthis.initGlobalConfig();\r\n\t},\r\n\tonShow: function() {\r\n\t\tconsole.log('App Show');\r\n\t},\r\n\tonHide: function() {\r\n\t\tconsole.log('App Hide');\r\n\t},\r\n\tmethods: {\r\n\t\t// 检查登录状态\r\n\t\tcheckLoginStatus() {\r\n\t\t\tconst token = uni.getStorageSync('userToken');\r\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\r\n\r\n\t\t\tif (token && userInfo) {\r\n\t\t\t\tconsole.log('用户已登录:', userInfo.username);\r\n\t\t\t} else {\r\n\t\t\t\tconsole.log('用户未登录');\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\t// 初始化全局配置\r\n\t\tinitGlobalConfig() {\r\n\t\t\t// 设置全局请求超时时间\r\n\t\t\tuni.setStorageSync('requestTimeout', 10000);\r\n\r\n\t\t\t// 设置全局错误处理\r\n\t\t\tuni.onNetworkStatusChange((res) => {\r\n\t\t\t\tif (!res.isConnected) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '网络连接异常',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style>\r\n/* 全局样式 */\r\npage {\r\n\tbackground-color: #F5F5F5;\r\n\tfont-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;\r\n\tline-height: 1.6;\r\n}\r\n\r\n/* 重置默认样式 */\r\n* {\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n/* 通用工具类 */\r\n.flex {\r\n\tdisplay: flex;\r\n}\r\n\r\n.flex-center {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n}\r\n\r\n.flex-between {\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n}\r\n\r\n.flex-column {\r\n\tdisplay: flex;\r\n\tflex-direction: column;\r\n}\r\n\r\n.text-center {\r\n\ttext-align: center;\r\n}\r\n\r\n.text-left {\r\n\ttext-align: left;\r\n}\r\n\r\n.text-right {\r\n\ttext-align: right;\r\n}\r\n\r\n.text-primary {\r\n\tcolor: #007AFF;\r\n}\r\n\r\n.text-success {\r\n\tcolor: #34C759;\r\n}\r\n\r\n.text-warning {\r\n\tcolor: #FF9500;\r\n}\r\n\r\n.text-error {\r\n\tcolor: #FF3B30;\r\n}\r\n\r\n.text-gray {\r\n\tcolor: #999999;\r\n}\r\n\r\n.bg-white {\r\n\tbackground-color: #FFFFFF;\r\n}\r\n\r\n.bg-gray {\r\n\tbackground-color: #F8F9FA;\r\n}\r\n\r\n.border-radius {\r\n\tborder-radius: 15rpx;\r\n}\r\n\r\n.box-shadow {\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n}\r\n\r\n.margin-top {\r\n\tmargin-top: 20rpx;\r\n}\r\n\r\n.margin-bottom {\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n.padding {\r\n\tpadding: 20rpx;\r\n}\r\n\r\n.padding-horizontal {\r\n\tpadding-left: 20rpx;\r\n\tpadding-right: 20rpx;\r\n}\r\n\r\n.padding-vertical {\r\n\tpadding-top: 20rpx;\r\n\tpadding-bottom: 20rpx;\r\n}\r\n\r\n/* 按钮样式 */\r\n.btn-primary {\r\n\tbackground-color: #007AFF;\r\n\tcolor: #FFFFFF;\r\n\tborder: none;\r\n\tborder-radius: 50rpx;\r\n\tfont-size: 28rpx;\r\n\tfont-weight: bold;\r\n\tpadding: 20rpx 40rpx;\r\n}\r\n\r\n.btn-secondary {\r\n\tbackground-color: #F8F9FA;\r\n\tcolor: #333333;\r\n\tborder: none;\r\n\tborder-radius: 50rpx;\r\n\tfont-size: 28rpx;\r\n\tpadding: 20rpx 40rpx;\r\n}\r\n\r\n.btn-outline {\r\n\tbackground-color: transparent;\r\n\tcolor: #007AFF;\r\n\tborder: 2rpx solid #007AFF;\r\n\tborder-radius: 50rpx;\r\n\tfont-size: 28rpx;\r\n\tpadding: 18rpx 40rpx;\r\n}\r\n\r\n/* 卡片样式 */\r\n.card {\r\n\tbackground-color: #FFFFFF;\r\n\tborder-radius: 20rpx;\r\n\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);\r\n\tpadding: 30rpx;\r\n\tmargin-bottom: 20rpx;\r\n}\r\n\r\n/* 列表项样式 */\r\n.list-item {\r\n\tbackground-color: #FFFFFF;\r\n\tpadding: 30rpx;\r\n\tborder-bottom: 1rpx solid #EEEEEE;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n}\r\n\r\n.list-item:last-child {\r\n\tborder-bottom: none;\r\n}\r\n\r\n/* 标签样式 */\r\n.tag {\r\n\tbackground-color: #F8F9FA;\r\n\tcolor: #666666;\r\n\tfont-size: 24rpx;\r\n\tpadding: 10rpx 20rpx;\r\n\tborder-radius: 15rpx;\r\n\tdisplay: inline-block;\r\n}\r\n\r\n.tag.primary {\r\n\tbackground-color: #E3F2FD;\r\n\tcolor: #007AFF;\r\n}\r\n\r\n.tag.success {\r\n\tbackground-color: #E8F5E8;\r\n\tcolor: #34C759;\r\n}\r\n\r\n.tag.warning {\r\n\tbackground-color: #FFF3E0;\r\n\tcolor: #FF9500;\r\n}\r\n\r\n.tag.error {\r\n\tbackground-color: #FCE4EC;\r\n\tcolor: #FF3B30;\r\n}\r\n\r\n/* 价格样式 */\r\n.price {\r\n\tcolor: #FF3B30;\r\n\tfont-weight: bold;\r\n\tfont-size: 32rpx;\r\n}\r\n\r\n.price-small {\r\n\tcolor: #FF3B30;\r\n\tfont-weight: bold;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n/* 状态样式 */\r\n.status-pending {\r\n\tcolor: #FF9500;\r\n}\r\n\r\n.status-approved {\r\n\tcolor: #34C759;\r\n}\r\n\r\n.status-rejected {\r\n\tcolor: #FF3B30;\r\n}\r\n\r\n/* 输入框样式 */\r\n.input-wrapper {\r\n\tbackground-color: #FFFFFF;\r\n\tborder: 2rpx solid #EEEEEE;\r\n\tborder-radius: 15rpx;\r\n\tpadding: 0 20rpx;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tmin-height: 80rpx;\r\n}\r\n\r\n.input-wrapper.focus {\r\n\tborder-color: #007AFF;\r\n}\r\n\r\n/* 加载动画 */\r\n@keyframes loading {\r\n\t0% {\r\n\t\ttransform: rotate(0deg);\r\n\t}\r\n\t100% {\r\n\t\ttransform: rotate(360deg);\r\n\t}\r\n}\r\n\r\n.loading {\r\n\tanimation: loading 1s linear infinite;\r\n}\r\n\r\n/* 渐入动画 */\r\n@keyframes fadeIn {\r\n\tfrom {\r\n\t\topacity: 0;\r\n\t\ttransform: translateY(20rpx);\r\n\t}\r\n\tto {\r\n\t\topacity: 1;\r\n\t\ttransform: translateY(0);\r\n\t}\r\n}\r\n\r\n.fade-in {\r\n\tanimation: fadeIn 0.3s ease-out;\r\n}\r\n</style>\r\n", "import mod from \"-!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754093677254\n      var cssReload = require(\"D:/web/wx小程序/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}